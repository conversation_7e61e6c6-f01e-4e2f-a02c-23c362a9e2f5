package org.dromara.common.excel.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

public class LocalTimeConverter implements Converter<LocalTime> {

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    @Override
    public Class<?> supportJavaTypeKey() {
        return LocalTime.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING; // Excel 单元格的数据类型是字符串
    }

    // 写入 Excel 时的转换
    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<LocalTime> context) {
        LocalTime localTime = context.getValue();
        return new WriteCellData<>(localTime.format(TIME_FORMATTER));
    }

    // 读取 Excel 时的转换（可选实现）
    @Override
    public LocalTime convertToJavaData(ReadConverterContext<?> context) {
        return LocalTime.parse(context.getReadCellData().getStringValue(), TIME_FORMATTER);
    }
}
