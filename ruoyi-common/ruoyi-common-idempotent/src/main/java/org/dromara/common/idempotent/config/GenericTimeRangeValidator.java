package org.dromara.common.idempotent.config;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.dromara.common.idempotent.annotation.ValidTimeRange;

import java.lang.reflect.Field;
import java.util.Date;

/**
 * 通用时间范围校验器
 * 用于校验对象中两个时间字段的范围关系
 */
public class GenericTimeRangeValidator implements ConstraintValidator<ValidTimeRange, Object> {

    private String startField;
    private String endField;
    private String message;

    @Override
    public void initialize(ValidTimeRange constraintAnnotation) {
        // 初始化注解中的参数
        this.startField = constraintAnnotation.startField();
        this.endField = constraintAnnotation.endField();
        this.message = constraintAnnotation.message();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return true; // 如果对象为空，不进行校验
        }

        try {
            // 使用反射获取目标对象的开始时间和结束时间字段
            Field startField = value.getClass().getDeclaredField(this.startField);
            Field endField = value.getClass().getDeclaredField(this.endField);

            // 确保字段是可访问的
            startField.setAccessible(true);
            endField.setAccessible(true);

            // 获取字段的值
            Object startValue = startField.get(value);
            Object endValue = endField.get(value);

            // 如果字段值为空，不进行校验（交给其他注解处理）
            if (startValue == null || endValue == null) {
                return true;
            }

            // 确保字段的类型是 Date
            if (startValue instanceof Date && endValue instanceof Date) {
                Date startDate = (Date) startValue;
                Date endDate = (Date) endValue;

                // 如果开始时间在结束时间之后，校验失败
                return !startDate.after(endDate);
            } else {
                throw new IllegalArgumentException("字段类型必须是 java.util.Date");
            }

        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException("校验失败: " + e.getMessage(), e);
        }
    }
}
