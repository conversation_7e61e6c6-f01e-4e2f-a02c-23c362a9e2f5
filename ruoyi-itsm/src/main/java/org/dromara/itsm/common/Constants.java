package org.dromara.itsm.common;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024/10/15 17:36
 */
public class Constants {

    /**
     * Prometheus node exporter 端口
     */
    public static final String PROMETHEUS_NODE_EXPORTER_PORT = "9100";

    /**
     * Prometheus step unit step单位
     */
    public static final String PROMETHEUS_STEP_UNIT_HOUR = "h";
    public static final String PROMETHEUS_STEP_UNIT_MINUTE = "m";
    public static final String PROMETHEUS_STEP_UNIT_SECOND = "s";

    /**
     * sys_dict_data 的 dict_type >>> resource_overview_pql
     */
    public static final String SYS_DICT_DATA_DICT_TYPE_RESOURCE_OVERVIEW_PQL = "resource_overview_pql";

    /**
     * itsm_file 的 record_type
     */
    // 工单附件
    public static final String ITSM_FILE_RECORD_TYPE_WORK_ORDER = "workOrder";
    // 工单审批附件
    public static final String ITSM_FILE_RECORD_TYPE_WORK_ORDER_APPROVAL = "workOrder_approval";
}
