package org.dromara.itsm.config;

import lombok.Data;
import org.dromara.itsm.util.prometheus.PrometheusApiUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

// 新增配置类（单独文件）
@Configuration
@ConfigurationProperties(prefix = "prometheus.network")
@Data
public class PrometheusNetworkConfig {
    private String defaultDevice = "eth0";
    private boolean autoDetect = true;

    public void setDefaultDevice(String defaultDevice) {
        this.defaultDevice = defaultDevice;
        PrometheusApiUtils.setNetworkDevice(defaultDevice);
    }

}
