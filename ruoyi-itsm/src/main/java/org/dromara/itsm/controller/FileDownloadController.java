//package org.dromara.itsm.controller;
//
//import cn.dev33.satoken.annotation.SaCheckPermission;
//import com.itextpdf.text.DocumentException;
//import lombok.extern.slf4j.Slf4j;
//import org.dromara.itsm.util.ImageWatermarkUtils;
//import org.dromara.itsm.util.PdfWatermarkUtils;
//import org.springframework.core.io.ByteArrayResource;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.MediaType;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.io.FileNotFoundException;
//import java.io.IOException;
//import java.io.InputStream;
//import java.net.URISyntaxException;
//import java.nio.file.Files;
//import java.nio.file.Paths;
//
//@RestController
//@RequestMapping("/itsm/emergencyPlan")
//@Slf4j
//public class FileDownloadController {
//
//    /**
//     * 下载 PDF 文件（自动添加水印）
//     */
//    @SaCheckPermission("itsm:emergencyPlan:export")
//    @PostMapping("/pdf")
//    public ResponseEntity<ByteArrayResource> downloadPdf(@RequestParam(required = false) String filename)
//        throws IOException, DocumentException {
//        // 1. 读取原始 PDF（示例从类路径读取，实际可替换为数据库/OSS）
//        InputStream inputStream = getClass().getResourceAsStream("/pdfs/" + "测试一下pdf是否加水印.pdf");
//
//        if (inputStream == null) {
//            throw new FileNotFoundException("PDF文件不存在");
//        }
//
//        // 2. 动态生成水印（示例水印内容，可替换为业务数据）
//        byte[] watermarkedPdf = PdfWatermarkUtils.addTextWatermark(inputStream,
//            "机密文件 - 用户ID: 1001", 0.3f);
//
//        // 3. 构建响应
//        return ResponseEntity.ok()
//            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
//            .contentType(MediaType.APPLICATION_PDF)
//            .body(new ByteArrayResource(watermarkedPdf));
//    }
//
////    /**
////     * 下载图片文件（自动添加水印）
////     */
////    @SaCheckPermission("itsm:emergencyPlan:export")
////    @PostMapping("/image")
////    public ResponseEntity<ByteArrayResource> downloadImage(@RequestParam(required = false) String filename) throws IOException, URISyntaxException {
////        // 1. 读取原始图片（示例从类路径读取）
////        byte[] originalImage = Files.readAllBytes(Paths.get(getClass().getResource("/images/" + "测试一下图片是否加水印.jpg").toURI()));
////
////        // 2. 添加动态水印
////        byte[] watermarkedImage = ImageWatermarkUtils.addTextWatermark(originalImage,
////            "内部使用 - " + LocalDate.now().toString());
////
////        // 3. 构建响应
////        return ResponseEntity.ok()
////            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=watermarked_" + filename)
////            .contentType(MediaType.IMAGE_PNG)
////            .body(new ByteArrayResource(watermarkedImage));
////    }
//
//
//    /**
//     * 下载带水印的图片
//     * @param watermarkText      水印文字
//     * @param alpha     透明度
//     * @param intervalX X间隔
//     * @param intervalY Y间隔
//     */
//    @SaCheckPermission("itsm:emergencyPlan:export")
//    @PostMapping(value = "/image", produces = MediaType.IMAGE_PNG_VALUE)
//    public ResponseEntity<ByteArrayResource> downloadImage(
//        @RequestParam(defaultValue = "机密文档") String watermarkText,
//        @RequestParam(defaultValue = "0.3") float alpha,
//        @RequestParam(defaultValue = "-30") double angle,
//        @RequestParam(defaultValue = "200") int intervalX,
//        @RequestParam(defaultValue = "150") int intervalY) throws Exception {
//
//        try {
//            byte[] originalImage = Files.readAllBytes(Paths.get(getClass()
//                .getResource("/images/" + "测试一下图片是否加水印.jpg").toURI()));
//            byte[] watermarked = ImageWatermarkUtils.addTiledWatermark(
//                originalImage,watermarkText, alpha,angle, intervalX, intervalY);
//
//            return ResponseEntity.ok()
//                .header(HttpHeaders.CONTENT_DISPOSITION,
//                    "attachment; filename=watermarked_" + "测试导出图片加水印")
//                .contentLength(watermarked.length)
//                .contentType(MediaType.IMAGE_PNG)
//                .body(new ByteArrayResource(watermarked));
//
//        } catch (IOException e) {
//            // Undertow的客户端中断异常处理
//            if (e.getMessage() != null &&
//                (e.getMessage().contains("Connection reset") || e.getMessage().contains("Broken pipe"))) {
//                return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT).build();
//            }
//            return ResponseEntity.internalServerError().build();
//        } catch (URISyntaxException e) {
//            throw new RuntimeException(e);
//        }
//    }
//}
