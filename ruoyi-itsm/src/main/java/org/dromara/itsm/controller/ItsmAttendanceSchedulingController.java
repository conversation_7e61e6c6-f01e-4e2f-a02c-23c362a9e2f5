package org.dromara.itsm.controller;


import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.itsm.domain.bo.attendance.ItsmAttendanceSchedulingBo;
import org.dromara.itsm.domain.vo.attendance.ItsmAttendanceSchedulingInfoVo;
import org.dromara.itsm.domain.vo.attendance.ItsmAttendanceSchedulingVo;
import org.dromara.itsm.service.IItsmAttendanceSchedulingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 排班管理
 *
 * <AUTHOR> kx
 * @date 2024-10-24
 */
@Validated
@RestController
@RequestMapping("/itsm/attendanceScheduling")
public class ItsmAttendanceSchedulingController extends BaseController {

    @Autowired
    private  IItsmAttendanceSchedulingService itsmAttendanceSchedulingService;

    /**
     * 查询排班管理列表
     */
    @SaCheckPermission("itsm:attendanceScheduling:list")
    @GetMapping("/list")
    public TableDataInfo<ItsmAttendanceSchedulingVo> list(ItsmAttendanceSchedulingBo bo, PageQuery pageQuery) {
        return itsmAttendanceSchedulingService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出排班管理列表
     */
    @SaCheckPermission("itsm:attendanceScheduling:export")
    @Log(title = "排班管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ItsmAttendanceSchedulingBo bo, HttpServletResponse response) {
        List<ItsmAttendanceSchedulingVo> list = itsmAttendanceSchedulingService.queryList(bo);
        ExcelUtil.exportExcel(list, "排班管理", ItsmAttendanceSchedulingVo.class, response);
    }

    /**
     * 获取排班管理详细信息
     *
     * @param schedulingId 主键
     */
    @SaCheckPermission("itsm:attendanceScheduling:query")
    @GetMapping("/{schedulingId}")
    public R<ItsmAttendanceSchedulingVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String schedulingId) {
        return R.ok(itsmAttendanceSchedulingService.queryById(schedulingId));
    }

    /**
     * 获取当前登录人员排班信息
     */
    @SaCheckPermission("itsm:attendanceScheduling:query")
    @GetMapping("/getItsmAttendanceSchedulingInfo")
    public R<ItsmAttendanceSchedulingInfoVo> getItsmAttendanceSchedulingInfo() {
        return R.ok(itsmAttendanceSchedulingService.getItsmAttendanceSchedulingInfo());
    }

    /**
     * 新增排班管理
     */
    @SaCheckPermission("itsm:attendanceScheduling:add")
    @Log(title = "排班管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ItsmAttendanceSchedulingBo bo) {
        return toAjax(itsmAttendanceSchedulingService.insertByBo(bo));
    }

    /**
     * 一键初始化流水
     */
    @SaCheckPermission("itsm:attendanceScheduling:initRecord")
    @Log(title = "排班管理", businessType = BusinessType.INSERT)
    @PostMapping("/initRecord")
    public R<Void> initRecord() {
        return toAjax(itsmAttendanceSchedulingService.initRecord());
    }

    /**
     * 修改排班管理
     */
    @SaCheckPermission("itsm:attendanceScheduling:edit")
    @Log(title = "排班管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ItsmAttendanceSchedulingBo bo) {
        return toAjax(itsmAttendanceSchedulingService.updateByBo(bo));
    }

    /**
     * 删除排班管理
     *
     * @param schedulingIds 主键串
     */
    @SaCheckPermission("itsm:attendanceScheduling:remove")
    @Log(title = "排班管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{schedulingIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] schedulingIds) {
        return toAjax(itsmAttendanceSchedulingService.deleteWithValidByIds(List.of(schedulingIds), true));
    }
}
