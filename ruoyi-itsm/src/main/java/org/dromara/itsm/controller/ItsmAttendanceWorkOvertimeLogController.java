package org.dromara.itsm.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.itsm.domain.bo.ItsmAttendanceWorkOvertimeLogBo;
import org.dromara.itsm.domain.vo.ItsmAttendanceWorkOvertimeLogExcelVo;
import org.dromara.itsm.domain.vo.ItsmAttendanceWorkOvertimeLogVo;
import org.dromara.itsm.service.IItsmAttendanceWorkOvertimeLogService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 加班申请
 *
 * <AUTHOR>
 * @date 2024-11-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/itsm/attendanceWorkOvertimeLog")
public class ItsmAttendanceWorkOvertimeLogController extends BaseController {

    private final IItsmAttendanceWorkOvertimeLogService itsmAttendanceWorkOvertimeLogService;

    /**
     * 查询加班申请列表
     */
    @SaCheckPermission("itsm:attendanceWorkOvertimeLog:list")
    @GetMapping("/list")
    public TableDataInfo<ItsmAttendanceWorkOvertimeLogVo> list(ItsmAttendanceWorkOvertimeLogBo bo, PageQuery pageQuery) {
        return itsmAttendanceWorkOvertimeLogService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出加班申请列表
     */
    @SaCheckPermission("itsm:attendanceWorkOvertimeLog:export")
    @Log(title = "加班申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ItsmAttendanceWorkOvertimeLogBo bo, HttpServletResponse response) {
        List<ItsmAttendanceWorkOvertimeLogExcelVo> list = itsmAttendanceWorkOvertimeLogService.queryExcelList(bo);
        ExcelUtil.exportExcel(list, "加班申请", ItsmAttendanceWorkOvertimeLogExcelVo.class, response);
    }

    /**
     * 获取加班申请详细信息
     *
     * @param overtimeId 主键
     */
    @SaCheckPermission("itsm:attendanceWorkOvertimeLog:query")
    @GetMapping("/{overtimeId}")
    public R<ItsmAttendanceWorkOvertimeLogVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String overtimeId) {
        return R.ok(itsmAttendanceWorkOvertimeLogService.queryById(overtimeId));
    }

    /**
     * 新增加班申请
     */
    @SaCheckPermission("itsm:attendanceWorkOvertimeLog:add")
    @Log(title = "加班申请", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<ItsmAttendanceWorkOvertimeLogVo> add(@Validated(AddGroup.class) @RequestBody ItsmAttendanceWorkOvertimeLogBo bo) {
        return R.ok(itsmAttendanceWorkOvertimeLogService.insertByBo(bo));
    }

    /**
     * 修改加班申请
     */
    @SaCheckPermission("itsm:attendanceWorkOvertimeLog:edit")
    @Log(title = "加班申请", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<ItsmAttendanceWorkOvertimeLogVo> edit(@Validated(EditGroup.class) @RequestBody ItsmAttendanceWorkOvertimeLogBo bo) {
        return R.ok(itsmAttendanceWorkOvertimeLogService.updateByBo(bo));
    }

    /**
     * 删除加班申请
     *
     * @param overtimeIds 主键串
     */
    @SaCheckPermission("itsm:attendanceWorkOvertimeLog:remove")
    @Log(title = "加班申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{overtimeIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] overtimeIds) {
        return toAjax(itsmAttendanceWorkOvertimeLogService.deleteWithValidByIds(List.of(overtimeIds), true));
    }
}
