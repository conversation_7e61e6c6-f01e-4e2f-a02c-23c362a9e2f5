package org.dromara.itsm.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.itsm.domain.bo.ItsmEmergencyPlanTypeBo;
import org.dromara.itsm.domain.vo.ItsmEmergencyPlanTypeVo;
import org.dromara.itsm.service.IItsmEmergencyPlanTypeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应急预案类型
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/itsm/emergencyPlanType")
public class ItsmEmergencyPlanTypeController extends BaseController {

    private final IItsmEmergencyPlanTypeService itsmEmergencyPlanTypeService;

    /**
     * 查询应急预案类型列表
     */
    @SaCheckPermission("itsm:emergencyPlanType:list")
    @GetMapping("/list")
    public TableDataInfo<ItsmEmergencyPlanTypeVo> list(ItsmEmergencyPlanTypeBo bo, PageQuery pageQuery) {
        return itsmEmergencyPlanTypeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出应急预案类型列表
     */
    @SaCheckPermission("itsm:emergencyPlanType:export")
    @Log(title = "应急预案类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ItsmEmergencyPlanTypeBo bo, HttpServletResponse response) {
        List<ItsmEmergencyPlanTypeVo> list = itsmEmergencyPlanTypeService.queryList(bo);
        ExcelUtil.exportExcel(list, "应急预案类型", ItsmEmergencyPlanTypeVo.class, response);
    }

    /**
     * 获取应急预案类型详细信息
     *
     * @param planTypeId 主键
     */
    @SaCheckPermission("itsm:emergencyPlanType:query")
    @GetMapping("/{planTypeId}")
    public R<ItsmEmergencyPlanTypeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String planTypeId) {
        return R.ok(itsmEmergencyPlanTypeService.queryById(planTypeId));
    }

    /**
     * 新增应急预案类型
     */
    @SaCheckPermission("itsm:emergencyPlanType:add")
    @Log(title = "应急预案类型", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ItsmEmergencyPlanTypeBo bo) {
        return toAjax(itsmEmergencyPlanTypeService.insertByBo(bo));
    }

    /**
     * 修改应急预案类型
     */
    @SaCheckPermission("itsm:emergencyPlanType:edit")
    @Log(title = "应急预案类型", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ItsmEmergencyPlanTypeBo bo) {
        return toAjax(itsmEmergencyPlanTypeService.updateByBo(bo));
    }

    /**
     * 删除应急预案类型
     *
     * @param planTypeIds 主键串
     */
    @SaCheckPermission("itsm:emergencyPlanType:remove")
    @Log(title = "应急预案类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{planTypeIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] planTypeIds) {
        return toAjax(itsmEmergencyPlanTypeService.deleteWithValidByIds(List.of(planTypeIds), true));
    }

    /**
     * 状态修改
     */
    @SaCheckPermission("itsm:emergencyPlanType:edit")
    @Log(title = "应急预案类型", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody ItsmEmergencyPlanTypeBo bo) {
        return toAjax(itsmEmergencyPlanTypeService.updateStatus(bo));
    }
}
