package org.dromara.itsm.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.itsm.domain.bo.ItsmProjectUserBo;
import org.dromara.itsm.domain.vo.ItsmAllProjectInfoVo;
import org.dromara.itsm.domain.vo.ItsmProjectUserExcelVo;
import org.dromara.itsm.domain.vo.ItsmProjectUserVo;
import org.dromara.itsm.domain.vo.ProjectSysUserInfoVo;
import org.dromara.itsm.service.IItsmProjectUserService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目人员信息
 *
 * <AUTHOR> Xu
 * @date 2024-10-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/itsm/projectUser")
public class ItsmProjectUserController extends BaseController {

    private final IItsmProjectUserService itsmProjectUserService;

    /**
     * 查询项目人员信息列表
     */
    @SaCheckPermission("itsm:projectUser:list")
    @GetMapping("/list")
    public TableDataInfo<ItsmProjectUserVo> list(ItsmProjectUserBo bo, PageQuery pageQuery) {
        return itsmProjectUserService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出项目人员信息列表
     */
    @SaCheckPermission("itsm:projectUser:export")
    @Log(title = "项目人员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ItsmProjectUserBo bo, HttpServletResponse response) {
        List<ItsmProjectUserExcelVo> list = itsmProjectUserService.queryExcelList(bo);
        ExcelUtil.exportExcel(list, "项目人员信息", ItsmProjectUserExcelVo.class, response);
    }

    /**
     * 获取项目人员信息详细信息
     *
     * @param userId 主键
     */
    @SaCheckPermission("itsm:projectUser:query")
    @GetMapping("/{userId}")
    public R<ItsmProjectUserVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String userId) {
        return R.ok(itsmProjectUserService.queryById(userId));
    }

    /**
     * 新增项目人员信息
     */
    @SaCheckPermission("itsm:projectUser:add")
    @Log(title = "项目人员信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ItsmProjectUserBo bo) {
        return toAjax(itsmProjectUserService.insertByBo(bo));
    }

    /**
     * 修改项目人员信息
     */
    @SaCheckPermission("itsm:projectUser:edit")
    @Log(title = "项目人员信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ItsmProjectUserBo bo) {
        return toAjax(itsmProjectUserService.updateByBo(bo));
    }

    /**
     * 删除项目人员信息
     *
     * @param userIds 主键串
     */
    @SaCheckPermission("itsm:projectUser:remove")
    @Log(title = "项目人员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] userIds) {
        return toAjax(itsmProjectUserService.deleteWithValidByIds(List.of(userIds), true));
    }

    @GetMapping("/queryByProjectSysUser")
    public R<List<ProjectSysUserInfoVo>> queryByProjectSysUser(ItsmProjectUserBo bo) {
        return R.ok(itsmProjectUserService.queryByProjectSysUser(bo.getProjectId(), bo.getSystemId()));
    }

    //获取当前登录用户的项目关联的项目，系统，人员信息
    @GetMapping("/getItsmAllProjectInfo")
    public R<List<ItsmAllProjectInfoVo>> getItsmAllProjectInfoVo() {
        return R.ok(itsmProjectUserService.getItsmAllProjectInfoVo());
    }
}
