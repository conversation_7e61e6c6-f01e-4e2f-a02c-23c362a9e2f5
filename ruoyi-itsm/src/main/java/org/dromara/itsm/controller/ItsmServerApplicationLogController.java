package org.dromara.itsm.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.itsm.domain.bo.ItsmServerApplicationLogBo;
import org.dromara.itsm.domain.vo.ItsmServerApplicationLogVo;
import org.dromara.itsm.service.IItsmServerApplicationLogService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 服务器检测是否异常日志log
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/itsm/serverApplicationLog")
public class ItsmServerApplicationLogController extends BaseController {

    private final IItsmServerApplicationLogService itsmServerApplicationLogService;

    /**
     * 查询服务器检测是否异常日志log列表
     */
    @SaCheckPermission("itsm:serverApplicationLog:list")
    @GetMapping("/list")
    public TableDataInfo<ItsmServerApplicationLogVo> list(ItsmServerApplicationLogBo bo, PageQuery pageQuery) {
        return itsmServerApplicationLogService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出服务器检测是否异常日志log列表
     */
    @SaCheckPermission("itsm:serverApplicationLog:export")
    @Log(title = "服务器检测是否异常日志log", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ItsmServerApplicationLogBo bo, HttpServletResponse response) {
        List<ItsmServerApplicationLogVo> list = itsmServerApplicationLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "服务器检测是否异常日志log", ItsmServerApplicationLogVo.class, response);
    }

    /**
     * 获取服务器检测是否异常日志log详细信息
     *
     * @param applicationLogId 主键
     */
    @SaCheckPermission("itsm:serverApplicationLog:query")
    @GetMapping("/{applicationLogId}")
    public R<ItsmServerApplicationLogVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String applicationLogId) {
        return R.ok(itsmServerApplicationLogService.queryById(applicationLogId));
    }

    /**
     * 新增服务器检测是否异常日志log
     */
    @SaCheckPermission("itsm:serverApplicationLog:add")
    @Log(title = "服务器检测是否异常日志log", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ItsmServerApplicationLogBo bo) {
        return toAjax(itsmServerApplicationLogService.insertByBo(bo));
    }

    /**
     * 修改服务器检测是否异常日志log
     */
    @SaCheckPermission("itsm:serverApplicationLog:edit")
    @Log(title = "服务器检测是否异常日志log", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ItsmServerApplicationLogBo bo) {
        return toAjax(itsmServerApplicationLogService.updateByBo(bo));
    }

    /**
     * 删除服务器检测是否异常日志log
     *
     * @param applicationLogIds 主键串
     */
    @SaCheckPermission("itsm:serverApplicationLog:remove")
    @Log(title = "服务器检测是否异常日志log", businessType = BusinessType.DELETE)
    @DeleteMapping("/{applicationLogIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] applicationLogIds) {
        return toAjax(itsmServerApplicationLogService.deleteWithValidByIds(List.of(applicationLogIds), true));
    }
}
