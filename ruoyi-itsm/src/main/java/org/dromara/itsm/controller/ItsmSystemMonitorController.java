package org.dromara.itsm.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjectUtil;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.itsm.domain.vo.CpuMonitorVo;
import org.dromara.itsm.domain.vo.DiskMonitorVo;
import org.dromara.itsm.domain.vo.MemoryMonitorVo;
import org.dromara.itsm.service.IItsmSystemMonitorService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/itsm/sysMonitor")
public class ItsmSystemMonitorController {
    private final IItsmSystemMonitorService systemMonitorService;

    @SaCheckPermission("itsm:systemMonitor:list")
    @GetMapping("/sysMonitorTree")
    public R<List<Tree<Long>>> sysMonitorTree() {
        return R.ok(systemMonitorService.selectSysMonitorTree());
    }

    @SaCheckPermission("itsm:systemMonitor:list")
    @GetMapping("/getAllMonitor/{id}")
    public R<Map<String,Object>> getAllMonitor(@PathVariable @NotNull String id) {
        Map<String,Object> map= systemMonitorService.getAllMonitor(id);
        return map.isEmpty()?R.fail("获取全部监控指标失败"):R.ok("获取全部监控指标成功", map);
    }

    @SaCheckPermission("itsm:systemMonitor:list")
    @GetMapping("/getCpuMonitor/{ip}")
    public R<CpuMonitorVo> getCpuMonitor(@PathVariable @NotNull String ip) {
        CpuMonitorVo cpuMonitorVo = systemMonitorService.getCpuMonitor_v1(ip);
        return ObjectUtil.isNull(cpuMonitorVo) ?R.fail("获取Cpu监控指标失败"):R.ok("获取Cpu监控指标成功", cpuMonitorVo);
    }

    @SaCheckPermission("itsm:systemMonitor:list")
    @GetMapping("/getMemoryMonitor/{ip}")
    public R<MemoryMonitorVo> getMemoryMonitor(@PathVariable @NotNull String ip) {
        MemoryMonitorVo memoryMonitorVo = systemMonitorService.getMemoryMonitor_v1(ip);
        return ObjectUtil.isNull(memoryMonitorVo) ?R.fail("获取内存监控指标失败"):R.ok("获取内存监控指标成功", memoryMonitorVo);
    }
    @SaCheckPermission("itsm:systemMonitor:list")
    @GetMapping("/getDiskMonitor/{ip}")
    public R<DiskMonitorVo> getDiskMonitor(@PathVariable @NotNull String ip) {
        DiskMonitorVo diskMonitorVo = systemMonitorService.getDiskMonitor(ip);
        return ObjectUtil.isNull(diskMonitorVo) ?R.fail("获取磁盘监控指标失败"):R.ok("获取磁盘监控指标成功", diskMonitorVo);
    }
}
