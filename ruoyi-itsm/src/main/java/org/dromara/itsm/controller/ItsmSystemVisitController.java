package org.dromara.itsm.controller;

import java.util.List;

import com.aizuda.snailjob.client.model.ExecuteResult;
import com.aizuda.snailjob.common.core.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.itsm.domain.bo.ItsmSystemVisitBo;
import org.dromara.itsm.domain.vo.ItsmSystemVisitExcelVo;
import org.dromara.itsm.domain.vo.ItsmSystemVisitVo;
import org.dromara.itsm.service.IItsmSystemVisitService;
import org.dromara.itsm.snailjob.ItsmSystemVisitCountExecutor;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 系统访问统计
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/itsm/systemVisit")
public class ItsmSystemVisitController extends BaseController {

    private final IItsmSystemVisitService itsmSystemVisitService;

    private final ItsmSystemVisitCountExecutor systemVisitCountExecutor;

    /**
     * 查询系统访问统计列表
     */
    @SaCheckPermission("itsm:systemVisit:list")
    @GetMapping("/list")
    public TableDataInfo<ItsmSystemVisitVo> list(ItsmSystemVisitBo bo, PageQuery pageQuery) {
        return itsmSystemVisitService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出系统访问统计列表
     */
    @SaCheckPermission("itsm:systemVisit:export")
    @Log(title = "系统访问统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ItsmSystemVisitBo bo, HttpServletResponse response) {
        List<ItsmSystemVisitExcelVo> list = itsmSystemVisitService.queryExcelList(bo);
        ExcelUtil.exportExcel(list, "系统访问统计", ItsmSystemVisitExcelVo.class, response);
    }

    /**
     * 获取系统访问统计详细信息
     *
     * @param visitId 主键
     */
    @SaCheckPermission("itsm:systemVisit:query")
    @GetMapping("/{visitId}")
    public R<ItsmSystemVisitVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String visitId) {
        return R.ok(itsmSystemVisitService.queryById(visitId));
    }

    /**
     * 新增系统访问统计
     */
    @SaCheckPermission("itsm:systemVisit:add")
    @Log(title = "系统访问统计", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ItsmSystemVisitBo bo) {
        return toAjax(itsmSystemVisitService.insertByBo(bo));
    }

    /**
     * 修改系统访问统计
     */
    @SaCheckPermission("itsm:systemVisit:edit")
    @Log(title = "系统访问统计", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ItsmSystemVisitBo bo) {
        return toAjax(itsmSystemVisitService.updateByBo(bo));
    }

    /**
     * 删除系统访问统计
     *
     * @param visitIds 主键串
     */
    @SaCheckPermission("itsm:systemVisit:remove")
    @Log(title = "系统访问统计", businessType = BusinessType.DELETE)
    @DeleteMapping("/{visitIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] visitIds) {
        return toAjax(itsmSystemVisitService.deleteWithValidByIds(List.of(visitIds), true));
    }

    /**
     * 同步系统访问统计
     * @return
     */
    @SaCheckPermission("itsm:systemVisit:synchronization")
    @GetMapping("/synchronization")
    public R synchronization(){
        ExecuteResult executeResult = systemVisitCountExecutor.jobExecute(null);
        if (executeResult.getStatus() == StatusEnum.YES.getStatus()){
            return R.ok();
        }else {
            return R.fail();
        }
    }
}
