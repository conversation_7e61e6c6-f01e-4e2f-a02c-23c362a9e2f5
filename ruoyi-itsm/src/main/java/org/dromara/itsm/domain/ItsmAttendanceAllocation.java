package org.dromara.itsm.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.sql.Time;

/**
 * 班次管理对象 itsm_attendance_allocation
 *
 * <AUTHOR> Li
 * @date 2024-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("itsm_attendance_allocation")
public class ItsmAttendanceAllocation extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 班次名称
     */
    private String shiftName;

    /**
     * 考勤地点
     */
    private String attendanceLocation;

    /**
     * 上班打卡时间
     */
    private Time punchInTime;

    /**
     * 下班打卡时间
     */
    private Time punchOutTime;

    /**
     * 状态，0-启用，1-停用
     */
    private String status;


    /**
     * 地图选择的经度
     */
    private Double longitude;

    /**
     * 地图选择的纬度
     */
    private Double latitude;

    /**
     * 地图选择的详细地址
     */
    private String mapLocation;

}
