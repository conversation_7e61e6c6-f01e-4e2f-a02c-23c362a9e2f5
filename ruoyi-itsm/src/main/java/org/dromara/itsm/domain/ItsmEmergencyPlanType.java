package org.dromara.itsm.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 应急预案类型对象 itsm_emergency_plan_type
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("itsm_emergency_plan_type")
public class ItsmEmergencyPlanType extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @TableId(value = "plan_type_id")
    private String planTypeId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 类型名称
     */
    private String name;


}
