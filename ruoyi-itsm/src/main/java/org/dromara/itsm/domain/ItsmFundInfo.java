package org.dromara.itsm.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 资金信息对象 itsm_fund_info
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("itsm_fund_info")
public class ItsmFundInfo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 资金id
     */
    @TableId(value = "fund_id")
    private String fundId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 所属项目id
     */
    private String projectId;

    /**
     * 款项批次
     */
    private String paymentBatch;

    /**
     * 计划付款金额
     */
    private Long planAmount;

    /**
     * 计划付款日期
     */
    private Date planPaymentTime;

    /**
     * 已付款金额
     */
    private Long paidAmount;

    /**
     * 实际付款日期
     */
    private Date actualPaymentTime;


}
