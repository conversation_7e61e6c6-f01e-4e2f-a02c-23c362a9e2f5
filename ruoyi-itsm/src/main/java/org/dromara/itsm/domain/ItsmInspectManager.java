package org.dromara.itsm.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

import java.io.Serial;

/**
 * 巡检记录信息对象 itsm_inspect_manager
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("itsm_inspect_manager")
public class ItsmInspectManager extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 巡检记录id
     */
    @TableId(value = "record_id")
    private String recordId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 巡检编号
     */
    private String recordNo;

    /**
     * 巡检时间
     */
    private Date inspectTime;

    /**
     * 所属项目id
     */
    private String projectId;

    /**
     * 所属系统id
     */
    private String systemId;

    /**
     * 巡检服务器IP
     */
    private String inspectIps;

    /**
     * 项目服务器总数
     */
    private String projectServerTotal;

    /**
     * 项目系统总数
     */
    private String projectSystemTotal;

    /**
     * 巡检服务器数量
     */
    private String inspectServerTotal;

    /**
     * 巡检系统数量
     */
    private String inspectSystemTotal;

    /**
     * 系统访问地址
     */
    private String systemVisitUrl;

    /**
     * 巡检人员
     */
    private String inpsectUserId;

    /**
     * 巡检人员电话
     */
    private String inspectUserPhone;

    /**
     * 审核人员
     */
    private String auditUserId;

    /**
     * 审核人员电话
     */
    private String auditUserPhone;

    /**
     * 服务器检查
     */
    private String serverCheckFlag;

    /**
     * 数据库检查
     */
    private String dbCheckFlag;

    /**
     * 中间件检查
     */
    private String middlewareCheckFlag;

    /**
     * 网络检查
     */
    private String networkCheckFlag;

    /**
     * 异常情况说明
     */
    private String description;


}
