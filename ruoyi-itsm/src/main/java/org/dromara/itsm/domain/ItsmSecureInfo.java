package org.dromara.itsm.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 安全信息对象 itsm_secure_info
 *
 * <AUTHOR> Li
 * @date 2024-11-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("itsm_secure_info")
public class ItsmSecureInfo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 安全id
     */
    @TableId(value = "secure_id")
    private String secureId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 所属项目id
     */
    private String projectId;

    /**
     * 所属系统id
     */
    private String systemId;

    /**
     * 信息安全投入（元）
     */
    private Long investFund;

    /**
     * 信息安全等保级别
     */
    private String secureLevel;

    /**
     * 是否完成等保测评
     */
    private String atInsuranceFlag;

    /**
     * 是否密码测评
     */
    private String pwdFlag;

    /**
     * 互联网地址
     */
    private String internet;

    /**
     * 是否对接物联网
     */
    private String iotFlag;

    /**
     * 是否对接视频共享平台
     */
    private String videoFlag;

    /**
     * 互联网接口地址
     */
    private String internetInterface;

    /**
     * 暴露互联网的端口
     */
    private String port;


}
