package org.dromara.itsm.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 合同信息管理对象 itsm_stipulation_info
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("itsm_stipulation_info")
public class ItsmStipulationInfo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 合同id
     */
    @TableId(value = "stipulation_id")
    private String stipulationId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 所属项目id
     */
    private String projectId;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同编号
     */
    private String contractNumber;

    /**
     * 签约甲方
     */
    private String signFirstParty;

    /**
     * 签约乙方
     */
    private String signSecondParty;

    /**
     * 合同金额
     */
    private Long amount;

    /**
     * 合同签订时间
     */
    private Date signDate;

    /**
     * 合同交付内容
     */
    private String submitContent;

    /**
     * 合同款项支付比例
     */
    private String paymentRatio;

    /**
     * 合同付款信息
     */
    private String paymentInfo;

    /**
     * 合同付款登记
     */
    private String paymentRegister;

    /**
     * 附件id
     */
//    private Long fileId;


}
