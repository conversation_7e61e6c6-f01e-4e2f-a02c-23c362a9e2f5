package org.dromara.itsm.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

import java.io.Serial;

/**
 * 系统信息对象 itsm_system_info
 *
 * <AUTHOR>
 * @date 2024-10-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("itsm_system_info")
public class ItsmSystemInfo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 系统id
     */
    @TableId(value = "system_id")
    private String systemId;

    /**
     * 所属项目id
     */
    private String projectId;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 系统状态
     */
    private String systemStatus;

    /**
     * 建设责任部门
     */
    private String constructRespDept;

    /**
     * 部门负责人
     */
    private String deptManager;

    /**
     * 负责人电话
     */
    private String deptManagerPhone;

    /**
     * 工作联系人
     */
    private String workLinkman;

    /**
     * 联系人电话
     */
    private String workLinkmanPhone;

    /**
     * 计划验收时间
     */
    private Date planAcceptanceDate;

    /**
     * 计划上线时间
     */
    private Date planOnlineDate;

    /**
     * 系统内容描述
     */
    private String systemContent;

    /**
     * 系统类型
     */
    private String systemType;

    /**
     * 系统架构
     */
    private String systemArchitecture;

    /**
     * 客户端类型
     */
    private String clientType;

    /**
     * 系统面向对象
     */
    private String systemObject;

    /**
     * 接入网络类型
     */
    private String networkType;

    /**
     * 是否完全国产化
     */
    private String localizationFlag;

    /**
     * 计划改造时间
     */
    private Date planChangeDate;

    /**
     * 系统风险等级评估
     */
    private String systemRiskAssessment;

    /**
     * 是否属于政务网站或政务新媒体
     */
    private String govSystemFlag;

    /**
     * 特殊情况说明
     */
    private String specialExplanation;

    /**
     * 是否与省有应用/数据交互
     */
    private String proExchangeFlag;

    /**
     * 是否有移动端
     */
    private String mobileAppFlag;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
