package org.dromara.itsm.domain.bo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.itsm.domain.ItsmAttendanceGoOutLog;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 外出记录视图对象 itsm_attendance_go_out_log
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ItsmAttendanceGoOutLog.class)
public class ItsmAttendanceGoOutLogExcelBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 外出记录id
     */
    private String goOutId;

    /**
     * 所属项目id
     */
    private String projectId;

    private String projectName;

    /**
     * 外出理由
     */
    private String reason;

    /**
     * 外出时间
     */
    private Date goOutTime;

    private String createBy;

    private String userName;


}
