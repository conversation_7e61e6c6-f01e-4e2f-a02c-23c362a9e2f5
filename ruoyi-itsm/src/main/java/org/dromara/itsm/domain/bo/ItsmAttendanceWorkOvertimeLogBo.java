package org.dromara.itsm.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.itsm.domain.ItsmAttendanceWorkOvertimeLog;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 加班申请业务对象 itsm_attendance_work_overtime_log
 *
 * <AUTHOR>
 * @date 2024-11-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItsmAttendanceWorkOvertimeLog.class, reverseConvertGenerate = false)
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ItsmAttendanceWorkOvertimeLogBo extends BaseEntity {

    /**
     * 加班id
     */
    @NotBlank(message = "加班id不能为空", groups = { EditGroup.class })
    private String overtimeId;

    /**
     * 所属项目id
     */
    @NotBlank(message = "所属项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectId;

    /**
     * 加班理由
     */
    @NotBlank(message = "加班理由不能为空", groups = { AddGroup.class, EditGroup.class })
    private String reason;

    /**
     * 加班开始时间
     */
    @NotNull(message = "加班开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 加班结束时间
     */
    @NotNull(message = "加班结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    //用户id列表
    private List<String> sysUserIds;

    //月开始时间
    private Date startMonthTime;

    //月结束时间
    private Date endMonthTime;

    //加班人
    private String sysUserId;

    /**
     * 办理状态
     */
    private String processingStatus;


}
