package org.dromara.itsm.domain.bo;

import org.dromara.itsm.domain.ItsmEmergencyPlanType;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.List;

/**
 * 应急预案类型业务对象 itsm_emergency_plan_type
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItsmEmergencyPlanType.class, reverseConvertGenerate = false)
public class ItsmEmergencyPlanTypeBo extends BaseEntity {

    /**
     *
     */
//    @NotBlank(message = "不能为空", groups = { EditGroup.class })
    private String planTypeId;

    private List<String> planTypeIds;

    /**
     * 状态（0正常 1停用）
     */
//    @NotBlank(message = "状态（0正常 1停用）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 类型名称
     */
    @NotBlank(message = "类型名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;


}
