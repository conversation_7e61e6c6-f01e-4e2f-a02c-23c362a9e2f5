package org.dromara.itsm.domain.bo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.itsm.domain.ItsmInspectManager;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 巡检记录信息视图对象 itsm_inspect_manager
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ItsmInspectManager.class)
public class ItsmInspectManagerExcelBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 巡检记录id
     */

    private String recordId;

    /**
     * 巡检编号
     */

    private String recordNo;

    /**
     * 巡检时间
     */

    private Date inspectTime;

    /**
     * 所属项目id
     */
    private String projectId;

    /**
     * 所属系统id
     */
    private String systemId;

    private String projectName;

    private String systemName;

    /**
     * 巡检服务器IP
     */
    private String inspectIps;

    /**
     * 项目服务器总数
     */
    private String projectServerTotal;

    /**
     * 项目系统总数
     */
    private String projectSystemTotal;

    /**
     * 巡检服务器数量
     */
    private String inspectServerTotal;

    /**
     * 巡检系统数量
     */
    private String inspectSystemTotal;

    /**
     * 系统访问地址
     */
    private String systemVisitUrl;

    /**
     * 巡检人员
     */
    private String inpsectUserId;

    private String inpsectUserName;

    /**
     * 巡检人员电话
     */
    private String inspectUserPhone;

    /**
     * 审核人员
     */
    private String auditUserId;

    private String auditUserName;

    /**
     * 审核人员电话
     */
    private String auditUserPhone;

    /**
     * 服务器检查
     */

    private String serverCheckFlag;

    /**
     * 数据库检查
     */

    private String dbCheckFlag;

    /**
     * 中间件检查
     */

    private String middlewareCheckFlag;

    /**
     * 网络检查
     */

    private String networkCheckFlag;


    private String description;


}
