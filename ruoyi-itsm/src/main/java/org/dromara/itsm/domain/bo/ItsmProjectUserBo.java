package org.dromara.itsm.domain.bo;

import org.dromara.itsm.domain.ItsmProjectUser;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 项目人员信息业务对象 itsm_project_user
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItsmProjectUser.class, reverseConvertGenerate = false)
public class ItsmProjectUserBo extends BaseEntity {

    /**
     * 人员id
     */
    @NotBlank(message = "人员id不能为空", groups = { EditGroup.class })
    private String userId;

    /**
     * 所属系统用户id
     */
    @NotBlank(message = "所属系统用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sysUserId;

    /**
     * 所属项目id
     */
    @NotBlank(message = "所属项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectId;

    /**
     * 所属系统id
     */
//    @NotBlank(message = "所属系统id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String systemId;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String idCard;

    /**
     * 是否在岗
     */
    @NotBlank(message = "是否在岗不能为空", groups = { AddGroup.class, EditGroup.class })
    private String atWorkFlag;

    /**
     * 服务开始时间
     */
    @NotNull(message = "服务开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date serviceBeginDate;

    /**
     * 用户所属公司名
     */
    @NotBlank(message = "用户所属公司名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userCompanyName;

    /**
     * 职位
     */
    @NotBlank(message = "职位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String position;

    /**
     * 政治面貌
     */
    @NotBlank(message = "政治面貌不能为空", groups = { AddGroup.class, EditGroup.class })
    private String politicsStatus;

    /**
     * 学历
     */
    @NotBlank(message = "学历不能为空", groups = { AddGroup.class, EditGroup.class })
    private String eduBackground;

    /**
     * 学位
     */
    @NotBlank(message = "学位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String acaDegree;

    /**
     * 专业
     */
    @NotBlank(message = "专业不能为空", groups = { AddGroup.class, EditGroup.class })
    private String major;

    /**
     * 毕业院校
     */
    @NotBlank(message = "毕业院校不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gradSchool;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String contactPhone;

    /**
     * 是否有配偶\直系血亲\三代以内旁系血亲\近姻亲在我单位工作情况
     */
    @NotBlank(message = "是否有配偶\\直系血亲三代以内旁系血亲\\近姻亲在我单位工作情况不能为空", groups = { AddGroup.class, EditGroup.class })
    private String haveRelativesInUnit;

    /**
     * 紧急联系人及联系电话
     */
    @NotBlank(message = "紧急联系人及联系电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String emergencyInfo;

    /**
     * 是否开通vpn
     */
    @NotBlank(message = "是否开通vpn不能为空", groups = { AddGroup.class, EditGroup.class })
    private String vpnFlag;

    private List<String> projectIds;

    private List<String> systemIds;

    private Long createDept;

}
