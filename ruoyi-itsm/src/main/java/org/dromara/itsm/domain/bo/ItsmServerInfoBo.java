package org.dromara.itsm.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.itsm.domain.ItsmServerInfo;

import java.util.List;

/**
 * 服务器信息业务对象 itsm_server_info
 *
 * <AUTHOR> Xu
 * @date 2024-10-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItsmServerInfo.class, reverseConvertGenerate = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ItsmServerInfoBo extends BaseEntity {

    /**
     * 服务器id
     */
    @NotBlank(message = "服务器id不能为空", groups = { EditGroup.class })
    private String serverId;

    /**
     * 所属系统id
     */
    @NotBlank(message = "所属系统id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String systemId;

    private List<String> systemIds;

    /**
     * 服务器名称
     */
    @NotBlank(message = "服务器名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String serverName;

    /**
     * 服务器类型
     */
    @NotBlank(message = "服务器类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String serverType;

    /**
     * 其他类型
     */
//    @NotBlank(message = "其他类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String otherType;

    /**
     * 是否部署中山市政务云平台
     */
    @NotBlank(message = "是否部署中山市政务云平台不能为空", groups = { AddGroup.class, EditGroup.class })
    private String zsGovCloudFlag;

    /**
     * 云主机托管位置
     */
//    @NotBlank(message = "云主机托管位置不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cloudLocation;

    /**
     * 操作系统名称及版本
     */
    @NotBlank(message = "操作系统名称及版本不能为空", groups = { AddGroup.class, EditGroup.class })
    private String osNameVersion;

    /**
     * 开发工具
     */
//    @NotBlank(message = "开发工具不能为空", groups = { AddGroup.class, EditGroup.class })
    private String devTool;

    /**
     * 数据库名称及版本
     */
//    @NotBlank(message = "数据库名称及版本不能为空", groups = { AddGroup.class, EditGroup.class })
    private String dbNameVersion;

    /**
     * 数据库容量
     */
//    @NotBlank(message = "数据库容量不能为空", groups = { AddGroup.class, EditGroup.class })
    private String dbCapacity;

    /**
     * 中间件名称及版本
     */
//    @NotBlank(message = "中间件名称及版本不能为空", groups = { AddGroup.class, EditGroup.class })
    private String middlewareNameVersion;

    /**
     * 其他软件
     */
//    @NotBlank(message = "其他软件不能为空", groups = { AddGroup.class, EditGroup.class })
    private String otherSoftware;

    /**
     * 党政网络ip
     */
//    @NotBlank(message = "党政网络ip不能为空", groups = { AddGroup.class, EditGroup.class })
    private String govNetworkIp;

    /**
     * 互联网ip
     */
//    @NotBlank(message = "互联网ip不能为空", groups = { AddGroup.class, EditGroup.class })
    private String internetIp;

    /**
     * 云主机ipv4地址
     */
//    @NotBlank(message = "云主机ipv4地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cloudServerIpv4;

    /**
     * 云主机ipv6地址
     */
//    @NotBlank(message = "云主机ipv6地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cloudServerIpv6;

    /**
     * 互联网访问地址
     */
//    @NotBlank(message = "互联网访问地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String internetUrl;

    /**
     * 政务网络访问地址
     */
//    @NotBlank(message = "政务网络访问地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String govNetworkUrl;

    /**
     * 系统后台地址
     */
//    @NotBlank(message = "系统后台地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String systemAdminUrl;

    /**
     * cpu（核）
     */
//    @NotBlank(message = "cpu（核）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cpu;

    /**
     * 内存（G）
     */
//    @NotBlank(message = "内存（G）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String memory;

    /**
     * 系统盘（G）
     */
//    @NotBlank(message = "系统盘（G）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String systemHardDisk;

    /**
     * 数据盘（G）
     */
//    @NotBlank(message = "数据盘（G）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String dataHardDisk;

    /**
     * 申请工单号
     */
//    @NotBlank(message = "申请工单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyOrderNo;

    /**
     * 开通端口信息
     */
//    @NotBlank(message = "开通端口信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String openPortInfo;

    /**
     * 端口用途
     */
//    @NotBlank(message = "端口用途不能为空", groups = { AddGroup.class, EditGroup.class })
    private String openPortUse;


}
