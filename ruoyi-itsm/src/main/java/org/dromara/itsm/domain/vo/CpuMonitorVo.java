package org.dromara.itsm.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CpuMonitorVo {
    /**
     * 使用率
     */
    private String usageRate;
    /**
     * 使用率折线图X轴
     */
    private List<String> usageRateXAxis;
    /**
     * 使用率折线图series数据，y轴数据
     */
    private List<SeriesDataVo> usageRateSeries;
}
