package org.dromara.itsm.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 普罗米修斯，io使用率vo
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class IoMonitorVo {

    /**
     * 使用率折线图X轴
     */
    private List<String> usageRateXAxis;

    /**
     * 使用率折线图series数据，y轴数据，上行速度
     */
    private List<SeriesDataVo> ioSeries;

}
