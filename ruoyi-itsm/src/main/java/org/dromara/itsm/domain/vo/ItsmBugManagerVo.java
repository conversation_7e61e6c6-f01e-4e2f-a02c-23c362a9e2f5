package org.dromara.itsm.domain.vo;

import java.util.Date;

import org.dromara.itsm.domain.ItsmBugManager;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 漏洞管理视图对象 itsm_bug_manager
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ItsmBugManager.class)
public class ItsmBugManagerVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 漏洞id
     */
    @ExcelProperty(value = "漏洞id")
    private String bugId;

    /**
     * 编号
     */
    @ExcelProperty(value = "编号")
    private String bugNo;

    /**
     * 所属项目id
     */
    @ExcelProperty(value = "所属项目id")
    private String projectId;

    /**
     * 所属系统id
     */
    @ExcelProperty(value = "所属系统id")
    private String systemId;

    /**
     * 负责人id
     */
    @ExcelProperty(value = "负责人id")
    private String chargeUserId;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String chargeUserPhone;

    /**
     * 发现时间
     */
    @ExcelProperty(value = "发现时间")
    private Date discoveryTime;

    /**
     * 系统名称
     */
    @ExcelProperty(value = "系统名称")
    private String systemName;

    /**
     * 云主机IP
     */
    @ExcelProperty(value = "云主机IP")
    private String cloudServerIpv4;

    /**
     * 资产名称
     */
    @ExcelProperty(value = "资产名称")
    private String assetName;

    /**
     * 个数
     */
    @ExcelProperty(value = "个数")
    private String num;

    /**
     * 风险级别
     */
    @ExcelProperty(value = "风险级别")
    private String riskLevel;

    /**
     * 漏洞分组
     */
    @ExcelProperty(value = "漏洞分组")
    private String bugGroup;

    /**
     * 漏洞描述
     */
    @ExcelProperty(value = "漏洞描述")
    private String description;

    /**
     * 解决办法
     */
    @ExcelProperty(value = "解决办法")
    private String solution;

    /**
     * 处理状态
     */
    @ExcelProperty(value = "处理状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "handle_status")
    private String processingStatus;

    /**
     * 处理时间
     */
    @ExcelProperty(value = "处理时间")
    private Date processingTime;


}
