package org.dromara.itsm.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.itsm.domain.bo.ItsmFundInfoExcelBo;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 资金信息视图对象 itsm_fund_info
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ItsmFundInfoExcelBo.class)
public class ItsmFundInfoExcelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 所属项目id
     */
    @ExcelProperty(value = "所属项目")
    private String projectName;

    /**
     * 款项批次
     */
    @ExcelProperty(value = "款项批次")
    private String paymentBatch;

    /**
     * 计划付款金额
     */
    @ExcelProperty(value = "计划付款金额")
    private Long planAmount;

    /**
     * 计划付款日期
     */
    @ExcelProperty(value = "计划付款日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planPaymentTime;

    /**
     * 已付款金额
     */
    @ExcelProperty(value = "已付款金额")
    private Long paidAmount;

    /**
     * 实际付款日期
     */
    @ExcelProperty(value = "实际付款日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date actualPaymentTime;


}
