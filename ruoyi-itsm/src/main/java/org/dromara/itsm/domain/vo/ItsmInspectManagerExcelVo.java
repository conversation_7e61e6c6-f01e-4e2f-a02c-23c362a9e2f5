package org.dromara.itsm.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.itsm.domain.bo.ItsmInspectManagerExcelBo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 巡检记录信息视图对象 itsm_inspect_manager
 *
 * <AUTHOR> Xu
 * @date 2024-10-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ItsmInspectManagerExcelBo.class)
public class ItsmInspectManagerExcelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 巡检编号
     */
    @ExcelProperty(value = "巡检编号")
    private String recordNo;

    /**
     * 巡检时间
     */
    @ExcelProperty(value = "巡检时间")
    @ColumnWidth(value = 40)
    private Date inspectTime;

    /**
     * 所属项目id
     */
    @ExcelProperty(value = "所属项目")
    private String projectName;

    /**
     * 所属系统id
     */
    @ExcelProperty(value = "所属系统")
    private String systemName;

    /**
     * 巡检服务器IP
     */
    @ExcelProperty(value = "巡检服务器IP")
    private String inspectIps;

    /**
     * 项目服务器总数
     */
    @ExcelProperty(value = "项目服务器总数")
    private String projectServerTotal;

    /**
     * 项目系统总数
     */
    @ExcelProperty(value = "项目系统总数")
    private String projectSystemTotal;

    /**
     * 巡检服务器数量
     */
    @ExcelProperty(value = "巡检服务器数量")
    private String inspectServerTotal;

    /**
     * 巡检系统数量
     */
    @ExcelProperty(value = "巡检系统数量")
    private String inspectSystemTotal;

    /**
     * 系统访问地址
     */
    @ExcelProperty(value = "系统访问地址")
    private String systemVisitUrl;

    /**
     * 巡检人员
     */
    @ExcelProperty(value = "巡检人员")
    private String inpsectUserName;

    /**
     * 巡检人员电话
     */
    @ExcelProperty(value = "巡检人员电话")
    private String inspectUserPhone;

    /**
     * 审核人员
     */
    @ExcelProperty(value = "审核人员")
    private String auditUserName;

    /**
     * 审核人员电话
     */
    @ExcelProperty(value = "审核人员电话")
    private String auditUserPhone;

    /**
     * 服务器检查
     */
    @ExcelProperty(value = "服务器检查", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "check_situation")
    private String serverCheckFlag;

    /**
     * 数据库检查
     */
    @ExcelProperty(value = "数据库检查", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "check_situation")
    private String dbCheckFlag;

    /**
     * 中间件检查
     */
    @ExcelProperty(value = "中间件检查", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "check_situation")
    private String middlewareCheckFlag;

    /**
     * 网络检查
     */
    @ExcelProperty(value = "网络检查", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "check_situation")
    private String networkCheckFlag;

    /**
     * 异常情况说明
     */
    @ExcelProperty(value = "异常情况说明")
    private String description;


}
