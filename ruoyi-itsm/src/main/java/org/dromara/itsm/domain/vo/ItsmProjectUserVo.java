package org.dromara.itsm.domain.vo;

import java.util.Date;

import org.dromara.itsm.domain.ItsmProjectUser;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 项目人员信息视图对象 itsm_project_user
 *
 * <AUTHOR> Xu
 * @date 2024-10-16
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ItsmProjectUser.class)
public class ItsmProjectUserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 人员id
     */
    @ExcelProperty(value = "人员id")
    private String userId;

    /**
     * 人员姓名
     */
    @ExcelProperty(value = "人员姓名")
    private String userName;

    /**
     * 所属系统用户id
     */
    @ExcelProperty(value = "所属系统用户id")
    private String sysUserId;

    /**
     * 所属项目id
     */
    @ExcelProperty(value = "所属项目id")
    private String projectId;

    /**
     * 所属系统id
     */
    @ExcelProperty(value = "所属系统id")
    private String systemId;

    /**
     * 身份证号
     */
    @ExcelProperty(value = "身份证号")
    private String idCard;

    /**
     * 是否在岗
     */
    @ExcelProperty(value = "是否在岗", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String atWorkFlag;

    /**
     * 服务开始时间
     */
    @ExcelProperty(value = "服务开始时间")
    private Date serviceBeginDate;

    /**
     * 用户所属公司名
     */
    @ExcelProperty(value = "用户所属公司名")
    private String userCompanyName;

    /**
     * 职位
     */
    @ExcelProperty(value = "职位")
    private String position;

    /**
     * 政治面貌
     */
    @ExcelProperty(value = "政治面貌", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "politics_status")
    private String politicsStatus;

    /**
     * 学历
     */
    @ExcelProperty(value = "学历", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "edu_background")
    private String eduBackground;

    /**
     * 学位
     */
    @ExcelProperty(value = "学位")
    private String acaDegree;

    /**
     * 专业
     */
    @ExcelProperty(value = "专业")
    private String major;

    /**
     * 毕业院校
     */
    @ExcelProperty(value = "毕业院校")
    private String gradSchool;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 是否有配偶\直系血亲\三代以内旁系血亲\近姻亲在我单位工作情况
     */
    @ExcelProperty(value = "是否有配偶\\直系血亲\\三代以内旁系血亲\\近姻亲在我单位工作情况", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String haveRelativesInUnit;

    /**
     * 紧急联系人及联系电话
     */
    @ExcelProperty(value = "紧急联系人及联系电话")
    private String emergencyInfo;

    /**
     * 是否开通vpn
     */
    @ExcelProperty(value = "是否开通vpn", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String vpnFlag;


}
