package org.dromara.itsm.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.itsm.domain.bo.ItsmSystemInfoExcelBo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 系统信息视图导出Excel对象 itsm_system_info
 *
 * <AUTHOR> Xu
 * @date 2024-10-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ItsmSystemInfoExcelBo.class)
public class ItsmSystemInfoExcelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 系统名称
     */
    @ExcelProperty(value = "系统名称")
    private String systemName;

    /**
     * 所属项目id
     */
    @ExcelProperty(value = "所属项目")
    private String projectName;

    /**
     * 系统状态
     */
    @ExcelProperty(value = "系统状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "system_status")
    private String systemStatus;

    /**
     * 建设责任部门
     */
    @ExcelProperty(value = "建设责任部门")
    private String constructRespDept;

    /**
     * 部门负责人
     */
    @ExcelProperty(value = "部门负责人")
    private String deptManager;

    /**
     * 负责人电话
     */
    @ExcelProperty(value = "负责人电话")
    private String deptManagerPhone;

    /**
     * 工作联系人
     */
    @ExcelProperty(value = "工作联系人")
    private String workLinkman;

    /**
     * 联系人电话
     */
    @ExcelProperty(value = "联系人电话")
    private String workLinkmanPhone;

    /**
     * 计划验收时间
     */
    @ExcelProperty(value = "计划验收时间")
    private Date planAcceptanceDate;

    /**
     * 计划上线时间
     */
    @ExcelProperty(value = "计划上线时间")
    private Date planOnlineDate;

    /**
     * 系统内容描述
     */
    @ExcelProperty(value = "系统内容描述")
    private String systemContent;

    /**
     * 系统类型
     */
    @ExcelProperty(value = "系统类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "system_type")
    private String systemType;

    /**
     * 系统架构
     */
    @ExcelProperty(value = "系统架构", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "system_architecture")
    private String systemArchitecture;

    /**
     * 客户端类型
     */
    @ExcelProperty(value = "客户端类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "client_type")
    private String clientType;

    /**
     * 系统面向对象
     */
    @ExcelProperty(value = "系统面向对象", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "system_object")
    private String systemObject;

    /**
     * 接入网络类型
     */
    @ExcelProperty(value = "接入网络类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "network_type")
    private String networkType;

    /**
     * 是否完全国产化
     */
    @ExcelProperty(value = "是否完全国产化", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String localizationFlag;

    /**
     * 计划改造时间
     */
    @ExcelProperty(value = "计划改造时间")
    private Date planChangeDate;

    /**
     * 系统风险等级评估
     */
    @ExcelProperty(value = "系统风险等级评估", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "system_risk_assessment")
    private String systemRiskAssessment;

    /**
     * 是否属于政务网站或政务新媒体
     */
    @ExcelProperty(value = "是否属于政务网站或政务新媒体", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String govSystemFlag;

    /**
     * 特殊情况说明
     */
    @ExcelProperty(value = "特殊情况说明")
    private String specialExplanation;

    /**
     * 是否与省有应用/数据交互
     */
    @ExcelProperty(value = "是否与省有应用/数据交互", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String proExchangeFlag;

    /**
     * 是否有移动端
     */
    @ExcelProperty(value = "是否有移动端", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String mobileAppFlag;


}
