package org.dromara.itsm.domain.vo.attendance;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.dromara.itsm.domain.ItsmAttendanceAllocation;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Time;



/**
 * 班次管理视图对象 itsm_attendance_allocation
 *
 * <AUTHOR> Li
 * @date 2024-10-17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ItsmAttendanceAllocation.class)
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ItsmAttendanceAllocationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 班次名称
     */
    @ExcelProperty(value = "班次名称")
    private String shiftName;

    /**
     * 考勤地点
     */
    @ExcelProperty(value = "考勤地点")
    private String attendanceLocation;

    /**
     * 上班打卡时间
     */
    @ExcelProperty(value = "上班打卡时间")
    private Time punchInTime;

    /**
     * 下班打卡时间
     */
    @ExcelProperty(value = "下班打卡时间")
    private Time punchOutTime;

    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 地图选择的经度
     */
    @ExcelProperty(value = "地图选择的经度")
    private Double longitude;

    /**
     * 地图选择的纬度
     */
    @ExcelProperty(value = "地图选择的纬度")
    private Double latitude;

    /**
     * 地图选择的详细地址
     */
    @ExcelProperty(value = "地图选择的详细地址")
    private String mapLocation;


}
