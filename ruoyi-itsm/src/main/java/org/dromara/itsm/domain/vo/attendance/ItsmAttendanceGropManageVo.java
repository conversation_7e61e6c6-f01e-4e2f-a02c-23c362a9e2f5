package org.dromara.itsm.domain.vo.attendance;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.itsm.domain.ItsmAttendanceGroupManage;

import java.io.Serial;
import java.io.Serializable;


/**
 * 考勤组视图对象 itsm_attendance_grop_manage
 *
 * <AUTHOR> Li
 * @date 2024-10-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ItsmAttendanceGroupManage.class)
public class ItsmAttendanceGropManageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 考勤组名称
     */
    @ExcelProperty(value = "考勤组名称")
    private String groupName;

    /**
     * 人数
     */
    @ExcelProperty(value = "人数")
    private Integer peopleNum;


    /**
     * 考勤时间
     */
    @ExcelProperty(value = "考勤时间")
    private String attendanceTime;

    /**
     * 班次id
     */
    private Long allocationId;


}
