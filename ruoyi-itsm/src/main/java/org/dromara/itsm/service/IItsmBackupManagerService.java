package org.dromara.itsm.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.itsm.domain.bo.ItsmBackupManagerBo;
import org.dromara.itsm.domain.vo.ItsmBackupManagerExcelVo;
import org.dromara.itsm.domain.vo.ItsmBackupManagerVo;

import java.util.Collection;
import java.util.List;

/**
 * 备份管理Service接口
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
public interface IItsmBackupManagerService {

    /**
     * 查询备份管理
     *
     * @param backupId 主键
     * @return 备份管理
     */
    ItsmBackupManagerVo queryById(String backupId);

    /**
     * 分页查询备份管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 备份管理分页列表
     */
    TableDataInfo<ItsmBackupManagerVo> queryPageList(ItsmBackupManagerBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的备份管理列表
     *
     * @param bo 查询条件
     * @return 备份管理列表
     */
    List<ItsmBackupManagerVo> queryList(ItsmBackupManagerBo bo);

    /**
     * 查询符合条件的备份管理列表(导出Excel)
     *
     * @param bo 查询条件
     * @return 备份管理列表
     */
    List<ItsmBackupManagerExcelVo> queryExcelList(ItsmBackupManagerBo bo);

    /**
     * 新增备份管理
     *
     * @param bo 备份管理
     * @return 是否新增成功
     */
    Boolean insertByBo(ItsmBackupManagerBo bo);

    /**
     * 修改备份管理
     *
     * @param bo 备份管理
     * @return 是否修改成功
     */
    Boolean updateByBo(ItsmBackupManagerBo bo);

    /**
     * 校验并批量删除备份管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
