package org.dromara.itsm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.itsm.domain.ItsmEmergencyPlanType;
import org.dromara.itsm.domain.bo.ItsmEmergencyPlanTypeBo;
import org.dromara.itsm.domain.vo.ItsmEmergencyPlanTypeVo;

import java.util.Collection;
import java.util.List;

/**
 * 应急预案类型Service接口
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
public interface IItsmEmergencyPlanTypeService extends IService<ItsmEmergencyPlanType> {

    /**
     * 查询应急预案类型
     *
     * @param planTypeId 主键
     * @return 应急预案类型
     */
    ItsmEmergencyPlanTypeVo queryById(String planTypeId);

    /**
     * 分页查询应急预案类型列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应急预案类型分页列表
     */
    TableDataInfo<ItsmEmergencyPlanTypeVo> queryPageList(ItsmEmergencyPlanTypeBo bo, PageQuery pageQuery);

    /**
     * 分页查询应急预案类型列表（数据大屏专用）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应急预案类型分页列表
     */
    TableDataInfo<ItsmEmergencyPlanTypeVo> queryMonitorPageList(ItsmEmergencyPlanTypeBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的应急预案类型列表
     *
     * @param bo 查询条件
     * @return 应急预案类型列表
     */
    List<ItsmEmergencyPlanTypeVo> queryList(ItsmEmergencyPlanTypeBo bo);


    /**
     * 查询符合条件的应急预案类型列表（测试带数据权限）
     *
     * @param bo 查询条件
     * @return 应急预案类型列表
     */
    List<ItsmEmergencyPlanTypeVo> queryListByPermission(ItsmEmergencyPlanTypeBo bo);

    /**
     * 新增应急预案类型
     *
     * @param bo 应急预案类型
     * @return 是否新增成功
     */
    Boolean insertByBo(ItsmEmergencyPlanTypeBo bo);

    /**
     * 修改应急预案类型
     *
     * @param bo 应急预案类型
     * @return 是否修改成功
     */
    Boolean updateByBo(ItsmEmergencyPlanTypeBo bo);

    /**
     * 校验并批量删除应急预案类型信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    /**
     * 修改应急预案类型状态
     * @param bo
     * @return
     */
    Boolean updateStatus(ItsmEmergencyPlanTypeBo bo);

}
