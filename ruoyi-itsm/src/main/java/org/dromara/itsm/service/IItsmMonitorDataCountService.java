package org.dromara.itsm.service;

import jakarta.validation.constraints.NotNull;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.itsm.common.TimeRangeType;
import org.dromara.itsm.domain.bo.*;
import org.dromara.itsm.domain.vo.*;
import org.dromara.system.domain.bo.SysUserBo;
import org.dromara.system.domain.vo.SysUserVo;

import java.util.List;
import java.util.Map;

/**
 * 数据大屏统一service接口
 *
 * <AUTHOR> Xu
 * @date 2024-10-16
 */
public interface IItsmMonitorDataCountService {

    /**
     * 获取项目详细信息
     * @param projectId 项目信息
     * @return 项目详情
     */
    ItsmProjectInfoVo queryById(@NotNull(message = "主键不能为空") String projectId);

    /**
     * 分页查询系统信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 系统信息分页列表
     */
    TableDataInfo<ItsmSystemInfoVo> querySystemPageList(ItsmSystemInfoBo bo, PageQuery pageQuery);

    /**
     * 统计当前系统总数，异常数，正常数
     */
    ItsmCountSystemVo countSystem();

    /**
     * 工单总览，统计当前工单总数，处理中，办结数，办结率，告警等级饼状图
     */
    ItsmWorkOrderTrendCountVo workOrderCountAndGrade(ItsmWorkOrderManagerBo bo);

    /**
     * 分页查询服务器信息列表
     */
    TableDataInfo<ItsmServerInfoVo> queryServerPageList(ItsmServerInfoBo bo, PageQuery pageQuery);

    /**
     * 分页查询工单信息列表
     */
    TableDataInfo<ItsmWorkOrderManagerVo> queryWordOrderPageList(ItsmWorkOrderManagerBo bo, PageQuery pageQuery);

    /**
     * 分页查询应急预案信息列表
     */
    TableDataInfo<ItsmEmergencyPlanMonitorVo> queryPlanPageList(ItsmEmergencyPlanBo bo, PageQuery pageQuery);

    /**
     * 统计当前服务器资源使用率
     */
    Map<String, Object> getAllMonitor(String id,TimeRangeType rangeType);

    /**
     * 获取cpu折线图
     * @param metricName
     * @param timeRange
     * @return
     */
    Map<String, Object> getMetricData(String metricName, String timeRange,String aggregation) throws Exception;

    /**
     * 获取系统对应的工单数量，top10柱状图
     * @return
     */
    Top10DataVO getWorkOrderCountBySystem(String range);

    /**
     * 处理数据大屏告警/工单趋势（次）
     */
    WorkOrderTrendVo getWorkOrderTrend(ItsmWorkOrderManagerBo bo);

    /**
     * 获取工单平均处理时间统计数据
     */
    WorkOrderProcessTimeVo getWorkOrderAverageProcessTime(ItsmWorkOrderManagerBo bo);

    /**
     * 获取项目人员信息列表
     */
    TableDataInfo<ItsmProjectUserVo> queryProjectUserPageList(ItsmProjectUserBo bo, PageQuery pageQuery);

    //获取当前登录用户的项目关联的项目，系统，人员信息（数据大屏专用）
    List<ItsmAllProjectInfoVo> getItsmAllProjectInfoVo();

    //查询用户列表（数据大屏）
    TableDataInfo<SysUserVo> selectPageUserList(SysUserBo user, PageQuery pageQuery);

    //查询应急预案类型列表（数据大屏）
    TableDataInfo<ItsmEmergencyPlanTypeVo> planTypeList(ItsmEmergencyPlanTypeBo bo, PageQuery pageQuery);
}
