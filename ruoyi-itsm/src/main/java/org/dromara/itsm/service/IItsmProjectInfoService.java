package org.dromara.itsm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.itsm.domain.ItsmProjectInfo;
import org.dromara.itsm.domain.bo.ItsmProjectInfoBo;
import org.dromara.itsm.domain.vo.ItsmProjectInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 项目信息Service接口
 *
 * <AUTHOR> Xu
 * @date 2024-10-12
 */
public interface IItsmProjectInfoService extends IService<ItsmProjectInfo> {

    /**
     * 查询项目信息
     *
     * @param projectId 主键
     * @return 项目信息
     */
    ItsmProjectInfoVo queryById(String projectId);

    /**
     * 通过项目名名查询项目
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    ItsmProjectInfoVo selectProjectByProjectName(String userName);

    /**
     * 分页查询项目信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目信息分页列表
     */
    TableDataInfo<ItsmProjectInfoVo> queryPageList(ItsmProjectInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的项目信息列表
     *
     * @param bo 查询条件
     * @return 项目信息列表
     */
    List<ItsmProjectInfoVo> queryList(ItsmProjectInfoBo bo);

    /**
     * 查询符合条件的项目信息列表,针对数据大屏
     *
     * @param bo 查询条件
     * @return 项目信息列表
     */
    List<ItsmProjectInfoVo> queryMonitorDataList(ItsmProjectInfoBo bo);

    /**
     * 新增项目信息
     *
     * @param bo 项目信息
     * @return 是否新增成功
     */
    Boolean insertByBo(ItsmProjectInfoBo bo);

    /**
     * 修改项目信息
     *
     * @param bo 项目信息
     * @return 是否修改成功
     */
    Boolean updateByBo(ItsmProjectInfoBo bo);

    /**
     * 校验并批量删除项目信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
