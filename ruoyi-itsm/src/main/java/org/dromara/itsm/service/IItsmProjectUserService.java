package org.dromara.itsm.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.itsm.domain.bo.ItsmProjectUserBo;
import org.dromara.itsm.domain.vo.*;

import java.util.Collection;
import java.util.List;

/**
 * 项目人员信息Service接口
 *
 * <AUTHOR> Xu
 * @date 2024-10-16
 */
public interface IItsmProjectUserService {

    /**
     * 查询项目人员信息
     *
     * @param userId 主键
     * @return 项目人员信息
     */
    ItsmProjectUserVo queryById(String userId);

    /**
     * 查询项目信息,包含人员总数，在岗，请假，异常（暂定账号如果停用就是异常），备份
     * @param projectId
     * @return
     */
    ItsmProjectInfoVo queryPeopleNumById_old(String projectId);

    /**
     * 查询项目信息,包含人员总数，在岗，请假，异常（暂定账号如果停用就是异常）
     *
     * @param projectId 主键
     * @return 项目信息
     */
    ItsmProjectInfoVo queryPeopleNumById(String projectId);

    /**
     * 查询项目信息,包含人员总数，在岗，请假，异常（暂定账号如果停用就是异常），针对移动端
     *
     * @param projectId 主键
     * @return 项目信息
     */
    ItsmProjectUserCountInfoVo getProjectUserCountInfo(String projectId);

    /**
     * 分页查询项目人员信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目人员信息分页列表
     */
    TableDataInfo<ItsmProjectUserVo> queryPageList(ItsmProjectUserBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的项目人员信息列表
     *
     * @param bo 查询条件
     * @return 项目人员信息列表
     */
    List<ItsmProjectUserVo> queryList(ItsmProjectUserBo bo);

    /**
     * 分页查询项目人员信息列表(数据大屏)
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 系统信息分页列表
     */
    TableDataInfo<ItsmProjectUserVo> queryMonitorPageList(ItsmProjectUserBo bo, PageQuery pageQuery);

    /**
     * 导出Excel数据
     * @param bo
     * @return
     */
    List<ItsmProjectUserExcelVo> queryExcelList(ItsmProjectUserBo bo);

    /**
     * 根据项目id、系统id 查找对应的人员列表信息
     * @param projectId
     * @param systemId
     * @return
     */
    List<ProjectSysUserInfoVo> queryByProjectSysUser(String projectId, String systemId);

    /**
     * 根据项目id、系统id 查找对应的人员列表信息（针对数据大屏）
     * @param projectId
     * @param systemId
     * @return
     */
    List<ProjectSysUserInfoVo> queryMonitorByProjectSysUser(String projectId, String systemId);

    /**
     * 获取项目，系统，以及所属人员
     */
    List<ItsmAllProjectInfoVo> getItsmAllProjectInfoVo();

    /**
     * 获取项目，系统，以及所属人员(数据大屏专用)
     */
    List<ItsmAllProjectInfoVo> getMonitorItsmAllProjectInfoVo();

    /**
     * 新增项目人员信息
     *
     * @param bo 项目人员信息
     * @return 是否新增成功
     */
    Boolean insertByBo(ItsmProjectUserBo bo);

    /**
     * 修改项目人员信息
     *
     * @param bo 项目人员信息
     * @return 是否修改成功
     */
    Boolean updateByBo(ItsmProjectUserBo bo);

    /**
     * 校验并批量删除项目人员信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    /**
     * 根据项目，系统列表查询项目人员信息
     */
    List<ProjectSysUserInfoVo> queryProjectSysUserByProjectAndSystemIds(String projectId, List<String> systemIds);

}
