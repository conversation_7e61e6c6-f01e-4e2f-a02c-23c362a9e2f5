package org.dromara.itsm.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.itsm.domain.bo.ItsmStipulationInfoBo;
import org.dromara.itsm.domain.vo.ItsmStipulationInfoExcelVo;
import org.dromara.itsm.domain.vo.ItsmStipulationInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 合同信息管理Service接口
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
public interface IItsmStipulationInfoService {

    /**
     * 查询合同信息管理
     *
     * @param stipulationId 主键
     * @return 合同信息管理
     */
    ItsmStipulationInfoVo queryById(String stipulationId);

    /**
     * 分页查询合同信息管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 合同信息管理分页列表
     */
    TableDataInfo<ItsmStipulationInfoVo> queryPageList(ItsmStipulationInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的合同信息管理列表
     *
     * @param bo 查询条件
     * @return 合同信息管理列表
     */
    List<ItsmStipulationInfoVo> queryList(ItsmStipulationInfoBo bo);

    /**
     * 查询符合条件的系统信息列表(导出Excel)
     *
     * @param bo 查询条件
     * @return 系统信息列表
     */
    List<ItsmStipulationInfoExcelVo> queryExcelList(ItsmStipulationInfoBo bo);

    /**
     * 新增合同信息管理
     *
     * @param bo 合同信息管理
     * @return 是否新增成功
     */
    Boolean insertByBo(ItsmStipulationInfoBo bo);

    /**
     * 修改合同信息管理
     *
     * @param bo 合同信息管理
     * @return 是否修改成功
     */
    Boolean updateByBo(ItsmStipulationInfoBo bo);

    /**
     * 校验并批量删除合同信息管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
