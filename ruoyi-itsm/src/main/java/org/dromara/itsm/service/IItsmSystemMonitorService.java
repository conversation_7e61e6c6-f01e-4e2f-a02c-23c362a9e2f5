package org.dromara.itsm.service;

import cn.hutool.core.lang.tree.Tree;
import org.dromara.itsm.common.TimeRangeType;
import org.dromara.itsm.domain.vo.*;

import java.util.List;
import java.util.Map;

public interface IItsmSystemMonitorService {
    List<Tree<Long>> selectSysMonitorTree();

    CpuMonitorVo getCpuMonitor(String ip);

    CpuMonitorVo getCpuMonitor_v1(String ip);

    CpuMonitorVo getCpuMonitorECharts(String ip);

    MemoryMonitorVo getMemoryMonitor(String ip);

    MemoryMonitorVo getMemoryMonitor_v1(String ip);

    DiskMonitorVo getDiskMonitor(String ip);

    Map<String,Object> getAllMonitor(String ip);

    /**
     * 根据时间范围获取cpu监控数据（数据大屏）
     * @param ip
     * @param rangeType 当天，本周，本月
     * @return
     */
    CpuMonitorVo getCpuMonitorByRange(String ip, TimeRangeType rangeType);

    /**
     * 根据时间范围获取内存监控数据（数据大屏）
     * @param ip
     * @param rangeType 当天，本周，本月
     * @return
     */
    MemoryMonitorVo getMemoryMonitorByRange(String ip,TimeRangeType rangeType);

    /**
     * 根据时间范围获取磁盘监控数据（数据大屏）
     * @param ip
     * @param rangeType 当天，本周，本月
     * @return
     */
    DiskMonitorVo getDiskMonitorByRange(String ip,TimeRangeType rangeType);

    /**
     * 根据时间范围获取带宽监控数据（数据大屏）
     * @param ip
     * @param rangeType 当天，本周，本月
     * @return
     */
    NetworkMonitorVo getNetworkMonitorByRange(String ip, TimeRangeType rangeType);


    /**
     * 根据时间范围获取IO监控数据（数据大屏）
     * @param ip
     * @param rangeType 当天，本周，本月
     * @return
     */
    IoMonitorVo getIoMonitorByRange(String ip, TimeRangeType rangeType);
}
