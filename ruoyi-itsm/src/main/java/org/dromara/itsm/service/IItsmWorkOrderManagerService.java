package org.dromara.itsm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.itsm.domain.ItsmWorkOrderManager;
import org.dromara.itsm.domain.bo.ItsmWorkOrderManagerBo;
import org.dromara.itsm.domain.vo.*;
import org.dromara.workflow.domain.vo.ActHistoryInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 工单管理信息Service接口
 *
 * <AUTHOR> Xu
 * @date 2024-10-18
 */
public interface IItsmWorkOrderManagerService {

    /**
     * 查询工单管理信息
     *
     * @param orderId 主键
     * @return 工单管理信息
     */
    ItsmWorkOrderManagerVo queryById(String orderId);

    /**
     * 分页查询工单管理信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工单管理信息分页列表
     */
    TableDataInfo<ItsmWorkOrderManagerVo> queryPageList(ItsmWorkOrderManagerBo bo, PageQuery pageQuery);

    /**
     * 分页查询工单管理信息列表（数据大屏）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工单管理信息分页列表
     */
    TableDataInfo<ItsmWorkOrderManagerVo> queryMonitorPageList(ItsmWorkOrderManagerBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的工单管理信息列表
     *
     * @param bo 查询条件
     * @return 工单管理信息列表
     */
    List<ItsmWorkOrderManagerVo> queryList(ItsmWorkOrderManagerBo bo);

    /**
     * 查询符合条件的工单管理信息列表(我的申请，待办理，已办理，所有申请)
     *
     * @param bo 查询条件
     * @return 工单管理信息列表
     */
    List<ItsmWorkOrderManagerExcelVo> queryOtherExcelList(ItsmWorkOrderManagerBo bo);

    /**
     * 查询符合条件的工单管理信息列表（导出Excel,待审核数据）
     *
     * @param bo 查询条件
     * @return 工单管理信息列表
     */
    List<ItsmWorkOrderManagerExcelVo> queryExcelList(ItsmWorkOrderManagerBo bo);

    /**
     * 新增工单管理信息
     *
     * @param bo 工单管理信息
     * @return 是否新增成功
     */
    Boolean insertByBo(ItsmWorkOrderManagerBo bo);

    /**
     * 新增工单管理信息（保函附件）
     * @param bo
     * @return
     */
    Boolean save(ItsmWorkOrderManagerBo bo);

    /**
     * 新增工单管理信息（工作流接入）
     * @param bo
     * @return
     */
    ItsmWorkOrderManagerVo addOrderFlow(ItsmWorkOrderManagerBo bo);

    /**
     * 修改工单管理信息
     *
     * @param bo 工单管理信息
     * @return 是否修改成功
     */
    ItsmWorkOrderManagerVo updateByBo(ItsmWorkOrderManagerBo bo);

    /**
     * 校验并批量删除工单管理信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    /**
     * 工单管理待审核
     */
    TableDataInfo<ItsmWorkOrderManagerApplyVo> getPageByTaskWait(ItsmWorkOrderManagerBo taskBo, PageQuery pageQuery);

    /**
     * 获取审批记录
     * @param historyRecord
     * @return
     */
    List<ItsmActHistoryInfoVo> getHistoryRecord(List<ActHistoryInfoVo> historyRecord);

    /**
     * 系统当天对应的工单数
     */
    Top10DataVO getWorkOrderCountForToday();

    /**
     * 系统最近7天的工单数
     */
    Top10DataVO getWorkOrderCountForWeek();

    /**
     * 系统最近30天的工单数
     */
    Top10DataVO getWorkOrderCountForMonth();

    //对接数据大屏，告警/工单平均处理时间（h）
    List<ItsmWorkOrderManager> getFinishedWorkOrders(LambdaQueryWrapper<ItsmWorkOrderManager> queryWrapper);
}
