package org.dromara.itsm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.itsm.domain.ItsmAttendanceGroupPeople;
import org.dromara.itsm.domain.bo.ItsmAttendanceGropPeopleBo;
import org.dromara.itsm.domain.vo.attendance.ItsmAttendanceGropPeopleVo;
import org.dromara.itsm.mapper.ItsmAttendanceGroupPeopleMapper;
import org.dromara.itsm.service.IItsmAttendanceGropPeopleService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 考勤组考勤人员Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@RequiredArgsConstructor
@Service
public class ItsmAttendanceGroupPeopleServiceImpl
    extends ServiceImpl<ItsmAttendanceGroupPeopleMapper, ItsmAttendanceGroupPeople> implements IItsmAttendanceGropPeopleService {

    private final ItsmAttendanceGroupPeopleMapper baseMapper;

    /**
     * 查询考勤组考勤人员
     *
     * @param id 主键
     * @return 考勤组考勤人员
     */
    @Override
    public ItsmAttendanceGropPeopleVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询考勤组考勤人员列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 考勤组考勤人员分页列表
     */
    @Override
    public TableDataInfo<ItsmAttendanceGropPeopleVo> queryPageList(ItsmAttendanceGropPeopleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ItsmAttendanceGroupPeople> lqw = buildQueryWrapper(bo);
        Page<ItsmAttendanceGropPeopleVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的考勤组考勤人员列表
     *
     * @param bo 查询条件
     * @return 考勤组考勤人员列表
     */
    @Override
    public List<ItsmAttendanceGropPeopleVo> queryList(ItsmAttendanceGropPeopleBo bo) {
        LambdaQueryWrapper<ItsmAttendanceGroupPeople> lqw = buildQueryWrapper(bo);
        if (LoginHelper.isSuperAdmin()){
            return TenantHelper.ignore(() ->baseMapper.selectVoList(lqw));
        }
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public Boolean insertBatch(List<ItsmAttendanceGroupPeople> peopleList) {
        return baseMapper.insertBatch(peopleList);
    }

    private LambdaQueryWrapper<ItsmAttendanceGroupPeople> buildQueryWrapper(ItsmAttendanceGropPeopleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ItsmAttendanceGroupPeople> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getGroupId() != null, ItsmAttendanceGroupPeople::getGroupId, bo.getGroupId());
        lqw.eq(bo.getSysUserId() != null, ItsmAttendanceGroupPeople::getSysUserId, bo.getSysUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), ItsmAttendanceGroupPeople::getType, bo.getType());
        lqw.in(CollectionUtil.isNotEmpty(bo.getGroupIds()), ItsmAttendanceGroupPeople::getGroupId, bo.getGroupIds());
        lqw.in(CollectionUtil.isNotEmpty(bo.getSysUserIds()), ItsmAttendanceGroupPeople::getSysUserId, bo.getSysUserIds());

        lqw.orderByDesc(BaseEntity::getCreateTime);
        return lqw;
    }

    /**
     * 新增考勤组考勤人员
     *
     * @param bo 考勤组考勤人员
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ItsmAttendanceGropPeopleBo bo) {
        ItsmAttendanceGroupPeople add = MapstructUtils.convert(bo, ItsmAttendanceGroupPeople.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改考勤组考勤人员
     *
     * @param bo 考勤组考勤人员
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ItsmAttendanceGropPeopleBo bo) {
        ItsmAttendanceGroupPeople update = MapstructUtils.convert(bo, ItsmAttendanceGroupPeople.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ItsmAttendanceGroupPeople entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除考勤组考勤人员信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public Boolean deleteByGroupIds(Collection<Long> groupIds) {
        return baseMapper.delete(
            new LambdaQueryWrapper<ItsmAttendanceGroupPeople>().in(ItsmAttendanceGroupPeople::getGroupId,groupIds)) >0;
    }
}
