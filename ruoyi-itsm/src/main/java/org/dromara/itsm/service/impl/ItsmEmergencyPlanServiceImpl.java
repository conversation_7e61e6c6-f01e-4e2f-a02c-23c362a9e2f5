package org.dromara.itsm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.itsm.domain.ItsmEmergencyPlan;
import org.dromara.itsm.domain.bo.ItsmEmergencyPlanBo;
import org.dromara.itsm.domain.vo.ItsmEmergencyPlanMonitorVo;
import org.dromara.itsm.domain.vo.ItsmEmergencyPlanVo;
import org.dromara.itsm.mapper.ItsmEmergencyPlanMapper;
import org.dromara.itsm.service.IItsmEmergencyPlanService;
import org.dromara.system.domain.vo.SysDeptVo;
import org.dromara.system.service.ISysDeptService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 应急预案Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ItsmEmergencyPlanServiceImpl implements IItsmEmergencyPlanService {

    private final ItsmEmergencyPlanMapper baseMapper;

    private final ISysDeptService deptService;

    /**
     * 查询应急预案
     *
     * @param planId 主键
     * @return 应急预案
     */
    @Override
    public ItsmEmergencyPlanVo queryById(String planId){
        return baseMapper.selectVoById(planId);
    }

    /**
     * 分页查询应急预案列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应急预案分页列表
     */
    @Override
    public TableDataInfo<ItsmEmergencyPlanVo> queryPageList(ItsmEmergencyPlanBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ItsmEmergencyPlan> lqw = buildQueryWrapper(bo);
        Page<ItsmEmergencyPlanVo> result = LoginHelper.isSuperAdmin()
            ? TenantHelper.ignore(() -> baseMapper.selectVoPage(pageQuery.build(), lqw))
            : baseMapper.customPageList(pageQuery.build(), lqw);
        log.info("分页查询应急预案结果：{}", JSON.toJSONString(result.getRecords(),true));
        if (CollectionUtil.isNotEmpty(result.getRecords())){
            List<Long> deptIds = result.getRecords().stream().map(ItsmEmergencyPlanVo::getCreateDept).distinct().toList();
            Map<Long, SysDeptVo> sysDeptVoByIds = deptService.getSysDeptVoByIds(deptIds);
            result.getRecords().forEach(r ->{
                if (CollectionUtil.isNotEmpty(sysDeptVoByIds)){
                    r.setCreateDeptStr(
                        ObjectUtil.isNotNull(sysDeptVoByIds.get(r.getCreateDept()))?sysDeptVoByIds.get(r.getCreateDept()).getDeptName():"");
                }
            });
        }
        return TableDataInfo.build(result);
    }

    @Override
    public TableDataInfo<ItsmEmergencyPlanMonitorVo> queryMonitorPageList(ItsmEmergencyPlanBo bo, PageQuery pageQuery) {
        // 直接查询带部门名称的结果
        Page<ItsmEmergencyPlanMonitorVo> result = baseMapper.selectMonitorPage(pageQuery.build(), bo);
        log.info("数据大屏，分页查询应急预案结果：{}", JSON.toJSONString(result.getRecords(),true));
        return TableDataInfo.build(result);
    }

    /**
     * 复制分页对象
     * @param sourcePage 源分页对象
     * @param targetClass 目标类
     * @return 复制后的分页对象
     * @param <T> 源类型
     * @param <V> 目标类型
     */
    private  <T, V> Page<V> copyPage(Page<T> sourcePage, Class<V> targetClass) {
        Page<V> targetPage = new Page<>();
        // 复制分页基本信息
        targetPage.setCurrent(sourcePage.getCurrent());
        targetPage.setSize(sourcePage.getSize());
        targetPage.setTotal(sourcePage.getTotal());
        targetPage.setPages(sourcePage.getPages());

        // 复制记录数据
        if (CollectionUtil.isNotEmpty(sourcePage.getRecords())) {
            List<V> records = MapstructUtils.convert(sourcePage.getRecords(), targetClass);
            targetPage.setRecords(records);
        }

        return targetPage;
    }

    /**
     * 查询符合条件的应急预案列表
     *
     * @param bo 查询条件
     * @return 应急预案列表
     */
    @Override
    public List<ItsmEmergencyPlanVo> queryList(ItsmEmergencyPlanBo bo) {
        LambdaQueryWrapper<ItsmEmergencyPlan> lqw = buildQueryWrapper(bo);
        return LoginHelper.isSuperAdmin()? TenantHelper.ignore(() -> baseMapper.selectVoList(lqw)): baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ItsmEmergencyPlan> buildQueryWrapper(ItsmEmergencyPlanBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ItsmEmergencyPlan> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ItsmEmergencyPlan::getStatus, bo.getStatus());
        lqw.eq(bo.getCreateDept() != null, ItsmEmergencyPlan::getCreateDept, bo.getCreateDept());
        lqw.eq(StringUtils.isNotBlank(bo.getPlanTypeId()), ItsmEmergencyPlan::getPlanTypeId, bo.getPlanTypeId());
        lqw.like(StringUtils.isNotBlank(bo.getName()), ItsmEmergencyPlan::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getOperator()), ItsmEmergencyPlan::getOperator, bo.getOperator());
        lqw.eq(bo.getDraftDate() != null, ItsmEmergencyPlan::getDraftDate, bo.getDraftDate());
        lqw.eq(bo.getCanvassDate() != null, ItsmEmergencyPlan::getCanvassDate, bo.getCanvassDate());
        lqw.eq(bo.getObsoleteDate() != null, ItsmEmergencyPlan::getObsoleteDate, bo.getObsoleteDate());
        lqw.in(CollectionUtil.isNotEmpty(bo.getPlanTypeIds()), ItsmEmergencyPlan::getPlanTypeId, bo.getPlanTypeIds());
        lqw.in(CollectionUtil.isNotEmpty(bo.getPlanIds()), ItsmEmergencyPlan::getPlanId, bo.getPlanIds());

        lqw.orderByDesc(BaseEntity::getCreateTime);
        return lqw;
    }

    /**
     * 新增应急预案
     *
     * @param bo 应急预案
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ItsmEmergencyPlanBo bo) {
        ItsmEmergencyPlan add = MapstructUtils.convert(bo, ItsmEmergencyPlan.class);
        add.setStatus("1");//默认草拟
        if ("0".equals(bo.getStatus())){
            add.setReleaseDate(new Date());
        } else if ("3".equals(bo.getStatus())){
            add.setObsoleteDate(new Date());
        }
        if (bo.getDraftDate() == null){
            add.setDraftDate(new Date());
        }
        if (bo.getCanvassDate() == null){
            add.setCanvassDate(new Date());
        }
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setPlanId(add.getPlanId());
        }
        return flag;
    }

    /**
     * 修改应急预案
     *
     * @param bo 应急预案
     * @return 是否修改成功
     */
    @Override
    @Transactional
    public Boolean updateByBo(ItsmEmergencyPlanBo bo) {
        ItsmEmergencyPlanVo itsmEmergencyPlanVo = queryById(bo.getPlanId());
        if (ObjectUtil.isNull(itsmEmergencyPlanVo)){
            throw new ServiceException("当前选择的修改的数据不存在");
        }
        if ("0".equals(itsmEmergencyPlanVo.getStatus())){
            throw new ServiceException("当前选择的修改的数据状态为发布，不能修改，请作废以后再进行修改");
        }
        ItsmEmergencyPlan update = MapstructUtils.convert(bo, ItsmEmergencyPlan.class);
        if ("0".equals(bo.getStatus())){
            update.setReleaseDate(new Date());
        } else if ("3".equals(bo.getStatus())){
            update.setObsoleteDate(new Date());
        }
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ItsmEmergencyPlan entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除应急预案信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //批量查询
            List<ItsmEmergencyPlanVo> list = baseMapper.selectVoList(new LambdaQueryWrapper<ItsmEmergencyPlan>()
                .in(ItsmEmergencyPlan::getPlanId, ids));
            if (CollectionUtil.isEmpty(list)){
                throw new ServiceException("当前选择的删除的数据不存在");
            }
            List<String> errorMsgList = new ArrayList<>();
            list.forEach(r ->{
                if ("0".equals(r.getStatus())){
                    errorMsgList.add(r.getName());
                }
            });
            if (CollectionUtil.isNotEmpty(errorMsgList)){
                throw new ServiceException("当前选择的删除的数据：" + JSON.toJSONString(errorMsgList) + "处于发布状态，请先作废再删除");
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    @Transactional
    public Boolean updateStatus(ItsmEmergencyPlanBo bo) {
        if (CollectionUtil.isEmpty(bo.getPlanIds())){
            throw new ServiceException("请至少选择一条您要修改的数据");
        }

        List<ItsmEmergencyPlan> plans = baseMapper.selectList(new LambdaQueryWrapper<ItsmEmergencyPlan>()
            .in(ItsmEmergencyPlan::getPlanId,bo.getPlanIds()));
        if (CollectionUtil.isEmpty(plans)){
            throw new ServiceException("当前选择的修改的数据不存在");
        }

        LambdaUpdateWrapper<ItsmEmergencyPlan> updateWrapper = Wrappers.lambdaUpdate(ItsmEmergencyPlan.class)
            .in(ItsmEmergencyPlan::getPlanId, bo.getPlanIds())
            .set(ItsmEmergencyPlan::getUpdateTime, new Date())
            .set(ItsmEmergencyPlan::getStatus, bo.getStatus());
        if ("0".equals(bo.getStatus())){
            //设置发布时间
            updateWrapper.set(ItsmEmergencyPlan::getReleaseDate, new Date());
        }
        if ("3".equals(bo.getStatus())){
            //设置作废时间
            updateWrapper.set(ItsmEmergencyPlan::getObsoleteDate, new Date());
        }

        int update = baseMapper.update(null, updateWrapper);
        if (update <= 0){
            throw new ServiceException("系统异常，请稍后再试");
        }
        return Boolean.TRUE;
    }

    @Override
    public Map<String, ItsmEmergencyPlanVo> selectPlanByIdsToMap(List<String> planIdIds) {
        if (CollectionUtil.isEmpty(planIdIds)){
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<ItsmEmergencyPlan> lqw = buildQueryWrapper(ItsmEmergencyPlanBo.builder()
            .planTypeIds(planIdIds)
            .build());
        if (LoginHelper.isSuperAdmin()){
            List<ItsmEmergencyPlanVo> planVoList = TenantHelper.ignore(()-> baseMapper.selectVoList(lqw));
            return planVoList.stream()
                .collect(Collectors.toMap(ItsmEmergencyPlanVo::getPlanId, it -> it));
        }else {
            List<ItsmEmergencyPlanVo> systemInfoVos = baseMapper.selectVoList(lqw);
            return systemInfoVos.stream()
                .collect(Collectors.toMap(ItsmEmergencyPlanVo::getPlanId, it -> it));
        }
    }
}
