package org.dromara.itsm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.constant.UserConstants;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.itsm.config.UploadProperties;
import org.dromara.itsm.domain.ItsmFile;
import org.dromara.itsm.domain.bo.ItsmFileBo;
import org.dromara.itsm.domain.vo.ItsmFileVo;
import org.dromara.itsm.mapper.ItsmFileMapper;
import org.dromara.itsm.service.IItsmFileService;
import org.dromara.system.domain.bo.SysDictDataBo;
import org.dromara.system.domain.vo.SysDictDataVo;
import org.dromara.system.service.ISysDictDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 附件信息Service业务层处理
 *
 * <AUTHOR> Xu
 * @date 2024-10-21
 */
@RequiredArgsConstructor
@Service
public class ItsmFileServiceImpl implements IItsmFileService {
    private static Logger LOGGER = LoggerFactory.getLogger(ItsmFileServiceImpl.class);

    private final ItsmFileMapper baseMapper;

    private final ISysDictDataService dictDataService;

    public final UploadProperties uploadProperties;

    /**
     * 查询附件信息
     *
     * @param fileId 主键
     * @return 附件信息
     */
    @Override
    public ItsmFileVo queryById(String fileId){
        return baseMapper.selectVoById(fileId);
    }

    /**
     * 分页查询附件信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 附件信息分页列表
     */
    @Override
    public TableDataInfo<ItsmFileVo> queryPageList(ItsmFileBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ItsmFile> lqw = buildQueryWrapper(bo);
        Page<ItsmFileVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的附件信息列表
     *
     * @param bo 查询条件
     * @return 附件信息列表
     */
    @Override
    public List<ItsmFileVo> queryList(ItsmFileBo bo) {
        LambdaQueryWrapper<ItsmFile> lqw = buildQueryWrapper(bo);
        if (LoginHelper.isSuperAdmin()){
            return TenantHelper.ignore(() -> baseMapper.selectVoList(lqw));
        }
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ItsmFile> buildQueryWrapper(ItsmFileBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ItsmFile> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getFileName()), ItsmFile::getFileName, bo.getFileName());
        lqw.eq(StringUtils.isNotBlank(bo.getFilePath()), ItsmFile::getFilePath, bo.getFilePath());
        lqw.eq(StringUtils.isNotBlank(bo.getFileSuffix()), ItsmFile::getFileSuffix, bo.getFileSuffix());
        lqw.eq(bo.getFileSize() != null, ItsmFile::getFileSize, bo.getFileSize());
        lqw.eq(StringUtils.isNotBlank(bo.getRecordType()), ItsmFile::getRecordType, bo.getRecordType());
        lqw.eq(StringUtils.isNotBlank(bo.getRecordId()), ItsmFile::getRecordId, bo.getRecordId());
        lqw.in(CollectionUtil.isNotEmpty(bo.getRecordIds()), ItsmFile::getRecordId, bo.getRecordId());
        lqw.eq(bo.getUploadTime() != null, ItsmFile::getUploadTime, bo.getUploadTime());
        lqw.eq(ItsmFile::getDelFlag, UserConstants.DEL_FLAG_NORMAL);

        lqw.orderByDesc(BaseEntity::getCreateTime);
        return lqw;
    }

    /**
     * 新增附件信息
     *
     * @param bo 附件信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ItsmFileBo bo) {
        ItsmFile add = MapstructUtils.convert(bo, ItsmFile.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setFileId(add.getFileId());
        }
        return flag;
    }

    /**
     * 修改附件信息
     *
     * @param bo 附件信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ItsmFileBo bo) {
        ItsmFile update = MapstructUtils.convert(bo, ItsmFile.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ItsmFile entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除附件信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public Boolean deleteWithValidByRecordIds(List<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        LambdaUpdateWrapper<ItsmFile> wrapper = new LambdaUpdateWrapper<ItsmFile>().in(ItsmFile::getFileId, ids);
        return baseMapper.delete(wrapper) > 0;
    }

    @Override
    public R<Void> uploadFile(MultipartFile multipartFile, String fileType, String recordId) {
        // 查询设置的文件大小及类型字典
        SysDictDataBo dictData = new SysDictDataBo();
        dictData.setDictType("file_size_type");
        dictData.setDictValue(fileType);
        List<SysDictDataVo> sysDictDataVos = TenantHelper.ignore(() -> dictDataService.selectDictDataList(dictData));
        if(CollectionUtils.isEmpty(sysDictDataVos)) {
            return R.fail("文件上传失败，未设置文件大小及格式！");
        }
        // 将字典信息映射到Map中
        Map<String, SysDictDataVo> dictDataVoMap = sysDictDataVos.stream()
            .collect(Collectors.toMap(SysDictDataVo::getDictValue, it -> it));
        SysDictDataVo vo = dictDataVoMap.get(fileType);
        if (ObjectUtil.isNull(vo)){
            return R.fail("文件上传失败，当前文件类型：" + fileType + "系统暂不支持上传");
        }

        String remark = vo.getRemark();
        int fileMaxSize = Integer.parseInt(remark.split("####")[0]);
        String fileFormat = remark.split("####")[1];
        if (!multipartFile.isEmpty()) {
            String originalfileName = multipartFile.getOriginalFilename();
            String suffix = FileUtil.extName(originalfileName);
            long fileSize = multipartFile.getSize();
            if(!fileFormat.contains(suffix)) {
                return R.fail("文件上传失败，请上传格式 " + fileFormat + " 的文件！");
            }
            if(fileSize > (long) fileMaxSize * 1024 * 1024) {
                return R.fail("文件上传失败，超过 " + fileMaxSize + " MB！");
            }

            String rootPath = uploadProperties.getRootPath();
            // 获取配置的文件夹路径
            String savePath = this.getConfigBasePath(fileType);
            File saveFile = new File(rootPath + savePath);
            if(!saveFile.exists()) {
                saveFile.mkdirs();
            }
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1;
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            String filePath = savePath + "/" + year + "/" + month + "/" + day;
            File file = new File(rootPath + filePath);
            if(!file.exists()) {
                file.mkdirs();
            }

            String uuid = IdUtil.simpleUUID();
            String newFileName = uuid + "." + suffix;
            String fullFilePath = filePath + "/" + newFileName;
            try {
                multipartFile.transferTo(new File(rootPath + fullFilePath));
            } catch (IOException e) {
                LOGGER.error("文件上传异常", e);
                return R.fail("文件上传异常，请联系管理员！");
            }

            ItsmFileBo itsmFileBo = new ItsmFileBo();
            itsmFileBo.setFileId(uuid);
            itsmFileBo.setFileName(originalfileName);
            itsmFileBo.setFilePath(fullFilePath);
            itsmFileBo.setFileSuffix(suffix);
            itsmFileBo.setFileSize(fileSize);
            itsmFileBo.setRecordType(fileType);
            itsmFileBo.setRecordId(recordId);
            itsmFileBo.setUploadTime(new Date());
            this.insertByBo(itsmFileBo);
            return R.ok(uuid);
        }
        return R.fail("上传文件大小为0，请重新上传！");
    }

    @Override
    public String getConfigBasePath(String type) {
        if("workOrder".equals(type)) {
            // 工单附件路径
            return uploadProperties.getWorkOrderBasePath();
        }
        if("stipulation".equals(type)) {
            // 合同附件路径
            return uploadProperties.getStipulationBasePath();
        }

        if("leaveInfo".equals(type)) {
            // 请假附件路径
            return uploadProperties.getLeaveBasePath();
        }
        return uploadProperties.getDefaultBasePath();
    }
}
