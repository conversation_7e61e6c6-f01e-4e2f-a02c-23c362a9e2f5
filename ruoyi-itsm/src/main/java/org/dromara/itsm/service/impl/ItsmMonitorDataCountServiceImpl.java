package org.dromara.itsm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.enums.BusinessStatusEnum;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.itsm.common.Constants;
import org.dromara.itsm.common.TimeRangeType;
import org.dromara.itsm.domain.ItsmServerInfo;
import org.dromara.itsm.domain.ItsmWorkOrderManager;
import org.dromara.itsm.domain.bo.*;
import org.dromara.itsm.domain.vo.*;
import org.dromara.itsm.service.*;
import org.dromara.itsm.util.prometheus.PrometheusApiUtils;
import org.dromara.system.domain.bo.SysUserBo;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据大屏Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ItsmMonitorDataCountServiceImpl implements IItsmMonitorDataCountService {

    private final IItsmProjectUserService itsmProjectUserService;

    private final IItsmSystemInfoService itsmSystemInfoService;

    private final IItsmServerInfoService itsmServerInfoService;

    private final IServerHealthCheckService serverHealthCheckService;

    private final IItsmWorkOrderManagerService itsmWorkOrderManagerService;

    private final IItsmEmergencyPlanTypeService itsmEmergencyPlanTypeService;

    private final IItsmEmergencyPlanService itsmEmergencyPlanService;

    private final IItsmSystemMonitorService systemMonitorService;

    private final ISysUserService userService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private static final String[] EMERGENCY_TYPES = {"0", "1", "3", "2"}; // 正常、紧急、警告、次要
    private static final String[] EMERGENCY_NAMES = {"正常", "紧急", "警告", "次要"};


    @Value("${prometheus-url.query-api-url}")
    private String prometheusUrl;

    @Override
    public ItsmProjectInfoVo queryById(String projectId) {
        return  itsmProjectUserService.queryPeopleNumById(projectId);
    }

    @Override
    public ItsmProjectUserCountInfoVo getProjectUserCountInfo(String projectId) {
        return  itsmProjectUserService.getProjectUserCountInfo(projectId);
    }

    @Override
    public TableDataInfo<ItsmSystemInfoVo> querySystemPageList(ItsmSystemInfoBo bo, PageQuery pageQuery) {
        return TenantHelper.ignore(() -> itsmSystemInfoService.queryMonitorPageList(bo, pageQuery));
    }

    @Override
    public TableDataInfo<ItsmProjectUserVo> queryProjectUserPageList(ItsmProjectUserBo bo, PageQuery pageQuery) {
        return TenantHelper.ignore(() -> itsmProjectUserService.queryMonitorPageList(bo, pageQuery));
    }

    @Override
    public List<ItsmAllProjectInfoVo> getItsmAllProjectInfoVo() {
        return TenantHelper.ignore(itsmProjectUserService::getMonitorItsmAllProjectInfoVo);
    }

    @Override
    public TableDataInfo<SysUserVo> selectPageUserList(SysUserBo user, PageQuery pageQuery) {
        return TenantHelper.ignore(() -> userService.selectMonitorPageUserList(user, pageQuery));
    }

    @Override
    public TableDataInfo<ItsmEmergencyPlanTypeVo> planTypeList(ItsmEmergencyPlanTypeBo bo, PageQuery pageQuery) {
        return TenantHelper.ignore(() -> itsmEmergencyPlanTypeService.queryMonitorPageList(bo, pageQuery));
    }

    @Override
    public TableDataInfo<ItsmServerInfoVo> queryServerPageList(ItsmServerInfoBo bo, PageQuery pageQuery) {
        return TenantHelper.ignore(() -> itsmServerInfoService.queryMonitorPageList(bo, pageQuery));
    }

    @Override
    public TableDataInfo<ItsmWorkOrderManagerVo> queryWordOrderPageList(ItsmWorkOrderManagerBo bo, PageQuery pageQuery) {
        return TenantHelper.ignore(() -> itsmWorkOrderManagerService.queryMonitorPageList(bo, pageQuery));
    }

    @Override
    public TableDataInfo<ItsmEmergencyPlanMonitorVo> queryPlanPageList(ItsmEmergencyPlanBo bo, PageQuery pageQuery) {
        return TenantHelper.ignore(() -> itsmEmergencyPlanService.queryMonitorPageList(bo, pageQuery));
    }

    @Override
    public Map<String, Object> getAllMonitor(String id,TimeRangeType rangeType) {
        ItsmServerInfo serverInfo = TenantHelper.ignore(() -> itsmServerInfoService.getById(id));
        log.info("serverInfo:{}",serverInfo);
        if (ObjectUtil.isNull(serverInfo)){
            log.error("数据大屏获取服务器资源统计，根据id：{}查询服务器数据结果为空：{}",id,serverInfo);
            return Collections.emptyMap();
        }
        String ip = serverInfo.getCloudServerIpv4();
        if (StringUtils.isBlank(ip)){
            log.error("数据大屏获取服务器资源统计，服务器id：{}还没有绑定具体服务器：{}",id,ip);
            return Collections.emptyMap();
        }
        CpuMonitorVo cpuMonitorVo = systemMonitorService.getCpuMonitorByRange(ip,rangeType);
        MemoryMonitorVo memoryMonitorVo = systemMonitorService.getMemoryMonitorByRange(ip,rangeType);
        DiskMonitorVo diskMonitorVo = systemMonitorService.getDiskMonitorByRange(ip,rangeType);
        NetworkMonitorVo networkMonitorVo = systemMonitorService.getNetworkMonitorByRange(ip,rangeType);
        IoMonitorVo ioMonitorVo = systemMonitorService.getIoMonitorByRange(ip,rangeType);
        String serverRunTime
            = PrometheusApiUtils.formatSecondTime(PrometheusApiUtils.getRunTime(ip, Constants.PROMETHEUS_NODE_EXPORTER_PORT));
        Map<String, Object> map = new HashMap<>();
        map.put("serverRunTime", serverRunTime);//系统运行时间
        map.put("cpuMonitorVo", cpuMonitorVo);
        map.put("memoryMonitorVo", memoryMonitorVo);
        map.put("diskMonitorVo", diskMonitorVo);
        map.put("networkMonitorVo", networkMonitorVo);
        map.put("ioMonitorVo", ioMonitorVo);
        return map;
    }

    @Override
    public Map<String, Object> getMetricData(String metricName, String timeRange, String aggregation) throws Exception {
        long start, end;
        int step;
        // 时间范围处理
        switch (timeRange) {
            case "today":
                start = ZonedDateTime.now().with(LocalTime.MIN).toEpochSecond();
                end = ZonedDateTime.now().toEpochSecond();
                step = 30;
                break;
            case "week":
                start = ZonedDateTime.now().minusWeeks(1).toEpochSecond();
                end = ZonedDateTime.now().toEpochSecond();
                step = 300;
                break;
            case "month":
                start = ZonedDateTime.now().minusMonths(1).toEpochSecond();
                end = ZonedDateTime.now().toEpochSecond();
                step = 3600;// 每小时采集一次数据
                break;
            default:
                throw new IllegalArgumentException("Invalid time range: " + timeRange);
        }

        // 构建 PromQL 查询语句，使用聚合函数（默认 avg）
        String aggFunction = (aggregation == null || StringUtils.isBlank(aggregation)) ? "avg" : aggregation;

//        String query = String.format("%s(%s)", aggFunction, metricName); // 如 avg(cpu_usage)
        // 构建 PromQL 查询语句，加入实例选择器
//        String queryWithInstanceSelector = String.format("%s(%s{%s=\"%s\"})", aggFunction, metricName, "instance", "*************:9100");

        List<String> instances = Arrays.asList("*************:9100", "*************:9100");

        // 构造 instance 正则表达式，确保 IP 和端口被正确转义
        String instanceRegex = String.join("|", instances);
        log.info("instanceRegex:{}",instanceRegex);
        // 构建完整的 PromQL 查询
        String query = String.format("%s(%s{instance=~\"%s\"})", aggFunction, metricName, instanceRegex);

        // 对查询参数进行URL编码
        String encodedQuery = URLEncoder.encode(query, StandardCharsets.UTF_8);
        // 步长转换为持续时间字符串
        String stepDuration = step + "s";

        String url = String.format("%s?query=%s&start=%.3f&end=%.3f&step=%s",
            prometheusUrl, query, (double) start, (double) end, stepDuration
        );
        log.info("Prometheus API URL: {}", url);

        String responseJson = fetchPrometheusResponse(url);
        log.info("Prometheus API Response: {}", responseJson);
        return parsePrometheusResponse(responseJson);
    }

    @Override
    public Top10DataVO getWorkOrderCountBySystem(String range) {
        // 仅做参数校验（视图层职责）
        if (!Set.of("today", "week", "moon").contains(range)) {
            throw new ServiceException("非法时间维度");
        }

        return switch (range.toLowerCase()) {
            case "today" -> TenantHelper.ignore(itsmWorkOrderManagerService::getWorkOrderCountForToday);
            case "week" -> TenantHelper.ignore(itsmWorkOrderManagerService::getWorkOrderCountForWeek);
            case "moon" -> TenantHelper.ignore(itsmWorkOrderManagerService::getWorkOrderCountForMonth);
            default -> throw new IllegalArgumentException("Invalid range parameter");
        };
    }

    @Override
    public WorkOrderTrendVo getWorkOrderTrend(ItsmWorkOrderManagerBo bo) {
        // 1. 获取日期范围
        DateRange dateRange = calculateDateRange();

        // 2. 查询工单数据
        List<ItsmWorkOrderManagerVo> workOrders = queryWorkOrdersInDateRange(dateRange,bo);
        log.info("获取工单趋势数据：指定时间范围内查询到工单信息,当前系统id:{}，对应数据：{}",
            bo.getSystemId(),
            JSON.toJSONString(workOrders,true));
        if (CollectionUtil.isEmpty(workOrders)) {
            log.warn("获取工单趋势数据：指定时间范围内未查询到工单信息");
            return buildEmptyWorkOrderTrendVo();
        }

        // 3. 按日期和紧急程度分组统计
        Map<LocalDate, Map<String, Long>> dailyEmergencyStats = aggregateWorkOrdersByDateAndEmergency(workOrders);

        // 4. 构建图表数据
        return buildWorkOrderTrendVo(dateRange.getDateLabels(), dailyEmergencyStats);
    }

    @Override
    public WorkOrderProcessTimeVo getWorkOrderAverageProcessTime(ItsmWorkOrderManagerBo bo) {
        // 获取今天和前5天的日期
        LocalDate today = LocalDate.now();
        List<LocalDate> dateList = new ArrayList<>();
        for (int i = 5; i >= 0; i--) {
            dateList.add(today.minusDays(i));
        }

        // 格式化日期为前端需要的格式 (MM-dd)
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M-d");
        List<String> dateStrList = dateList.stream()
            .map(date -> date.format(formatter))
            .collect(Collectors.toList());

        // 查询已完成的工单数据
        List<ItsmWorkOrderManager> workOrders = getFinishedWorkOrders(dateList.get(0), today,bo);

        // 按紧急程度和日期分组计算平均处理时间
        Map<String, Map<LocalDate, List<Double>>> processingTimeMap = calculateProcessingTime(workOrders, dateList);

        // 构建返回数据
        return buildResponseData(dateStrList, processingTimeMap);
    }

    private List<ItsmWorkOrderManager> getFinishedWorkOrders(LocalDate startDate, LocalDate endDate,ItsmWorkOrderManagerBo bo) {
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.plusDays(1).atStartOfDay();

        LambdaQueryWrapper<ItsmWorkOrderManager> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ItsmWorkOrderManager::getProcessingStatus, BusinessStatusEnum.FINISH.getStatus())
            .isNotNull(ItsmWorkOrderManager::getFeedbackTime)
            .isNotNull(ItsmWorkOrderManager::getOverCancleTime)
            .eq(StringUtils.isNotBlank(bo.getSystemId()),ItsmWorkOrderManager::getSystemId,bo.getSystemId())
            .ge(ItsmWorkOrderManager::getFeedbackTime, startDateTime)
            .lt(ItsmWorkOrderManager::getFeedbackTime, endDateTime);
        return TenantHelper.ignore(() ->itsmWorkOrderManagerService.getFinishedWorkOrders(queryWrapper));
    }

    private Map<String, Map<LocalDate, List<Double>>> calculateProcessingTime(
        List<ItsmWorkOrderManager> workOrders, List<LocalDate> dateList) {

        Map<String, Map<LocalDate, List<Double>>> result = new HashMap<>();

        // 初始化结果Map
        for (String emergencyType : EMERGENCY_TYPES) {
            Map<LocalDate, List<Double>> dateMap = new HashMap<>();
            for (LocalDate date : dateList) {
                dateMap.put(date, new ArrayList<>());
            }
            result.put(emergencyType, dateMap);
        }

        // 计算每个工单的处理时间并按紧急程度和日期分组
        for (ItsmWorkOrderManager workOrder : workOrders) {
            String emergency = workOrder.getEmergency();
            // 获取反馈时间和办结时间并转换为LocalDateTime
            LocalDateTime feedbackTime = dateToLocalDateTime(workOrder.getFeedbackTime());
            LocalDateTime overTime = dateToLocalDateTime(workOrder.getOverCancleTime());
            if (feedbackTime != null && overTime != null) {
                // 计算处理时间（小时）
                double processingHours = ChronoUnit.MINUTES.between(feedbackTime, overTime) / 60.0;

                // 获取反馈日期
                LocalDate feedbackDate = feedbackTime.toLocalDate();

                // 如果日期在查询范围内，添加到对应的分组
                if (dateList.contains(feedbackDate) && Arrays.asList(EMERGENCY_TYPES).contains(emergency)) {
                    result.get(emergency).get(feedbackDate).add(processingHours);
                }
            }
        }
        return result;
    }

    private WorkOrderProcessTimeVo buildResponseData(
        List<String> dateStrList,
        Map<String, Map<LocalDate, List<Double>>> processingTimeMap) {

        WorkOrderProcessTimeVo workOrderProcessTimeVo = new WorkOrderProcessTimeVo();

        // 设置图例数据
        Map<String, Object> legend = new HashMap<>();
        legend.put("data", EMERGENCY_NAMES);
        workOrderProcessTimeVo.setLegend(legend);

        // 设置X轴数据
        Map<String, Object> xAxis = new HashMap<>();
        xAxis.put("data", dateStrList);
        workOrderProcessTimeVo.setXAxis(xAxis);

        // 设置系列数据
        List<Map<String, Object>> series = new ArrayList<>();

        for (int i = 0; i < EMERGENCY_TYPES.length; i++) {
            String emergencyType = EMERGENCY_TYPES[i];
            String emergencyName = EMERGENCY_NAMES[i];

            Map<String, Object> seriesItem = new HashMap<>();
            seriesItem.put("name", emergencyName);

            List<String> data = new ArrayList<>();
            for (LocalDate date : processingTimeMap.get(emergencyType).keySet().stream()
                .sorted().toList()) {

                List<Double> timeList = processingTimeMap.get(emergencyType).get(date);
                double avgTime = timeList.isEmpty() ? 0 :
                    timeList.stream().mapToDouble(Double::doubleValue).average().orElse(0);

                // 四舍五入到整数
                data.add(String.valueOf(Math.round(avgTime)));
            }

            seriesItem.put("data", data);
            series.add(seriesItem);
        }
        workOrderProcessTimeVo.setSeries(series);
        return workOrderProcessTimeVo;
    }

    private String fetchPrometheusResponse(String url) {
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder()
            .url(url)
            .build();

        try (Response response = client.newCall(request).execute()) {
            log.info("Prometheus API Response: {}", response.body());
            if (response.isSuccessful()) {
                return response.body().string();
            } else {
                throw new RuntimeException("Failed to fetch data from Prometheus: " + response.code());
            }
        } catch (Exception e) {
            log.error("Error fetching data from Prometheus at URL: {}. Error message: {}", url, e.getMessage());
            throw new RuntimeException("Failed to fetch data from Prometheus", e);
        }
    }
    private Map<String, Object> parsePrometheusResponse(String json) throws Exception {
        JsonNode root = objectMapper.readTree(json);
        JsonNode resultsNode = root.path("data").path("result");

        List<Map<String, Object>> series = new ArrayList<>();
        if (resultsNode.isArray()) {
            for (JsonNode result : resultsNode) {
                List<Map<String, Object>> dataPoints = new ArrayList<>();
                JsonNode values = result.path("values");
                if (values.isArray()) {
                    for (JsonNode value : values) {
                        long timestamp = value.get(0).asLong();
                        double usage = value.get(1).asDouble();
                        String timeStr = Instant.ofEpochSecond(timestamp).atZone(ZoneId.systemDefault())
                            .format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);

                        Map<String, Object> point = new HashMap<>();
                        point.put("x", timeStr);
                        point.put("y", usage);
                        dataPoints.add(point);
                    }
                }
                Map<String, Object> seriesItem = new HashMap<>();
                seriesItem.put("name", "Usage"); // 可以根据实际情况设置名称
                seriesItem.put("data", dataPoints);
                series.add(seriesItem);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("series", series);
        return result;
    }

    @Override
    public ItsmCountSystemVo countSystem() {
        List<ItsmSystemInfoVo> systemInfoVos = getSystemInfoList();
        if (CollectionUtil.isEmpty(systemInfoVos)) {
            log.error("统计系统数据接口，查询不到系统信息");
            return buildEmptyCountSystemVo();
        }

        List<String> systemIds = extractSystemIds(systemInfoVos);
        List<ItsmServerInfoVo> serverInfoVos = getServerInfoList(systemIds);
        if (CollectionUtil.isEmpty(serverInfoVos)) {
            return buildEmptyCountSystemVo();
        }

        Map<String, List<ItsmServerInfoVo>> ipv4ServerMap = groupServersByIPv4(serverInfoVos);
        Map<String, HealthStatus> healthStatusMap = checkServerHealth(ipv4ServerMap.keySet());

        return buildCountSystemVo(systemInfoVos, systemIds, ipv4ServerMap, healthStatusMap);
    }

    @Override
    public ItsmWorkOrderTrendCountVo workOrderCountAndGrade(ItsmWorkOrderManagerBo bo) {
        // 查询系统工单总数
        List<ItsmWorkOrderManagerVo> itsmWorkOrderManagerVos
            = TenantHelper.ignore(() -> itsmWorkOrderManagerService.queryList(bo));
        log.info("统计工单总览中的左边部分接口，查询到系统工单信息，未过滤前一共:{}条", itsmWorkOrderManagerVos.size());
        if (CollectionUtil.isEmpty(itsmWorkOrderManagerVos)) {
            log.error("统计工单总览中的左边部分接口，查询不到系统工单信息");
            return buildEmptyWorkOrderTrendCountVo();
        }

        List<ItsmWorkOrderManagerVo> filterItsmWorkOrderManagerVos = itsmWorkOrderManagerVos.stream()
            .filter(vo ->
                StringUtils.isNotBlank(vo.getProcessingStatus()) && StringUtils.isNotBlank(vo.getEmergency()))
            .toList();
        log.info("统计工单总览中的左边部分接口，查询到系统工单信息，未过滤后一共:{}条", itsmWorkOrderManagerVos.size());

        // 工单总数
        long total = filterItsmWorkOrderManagerVos.size();

        // 使用一次流操作来获取所有需要的数据，并排除 processingStatus 和 emergency 都为空的情况
        Map<String, Long> statusCountMap = filterItsmWorkOrderManagerVos.stream()
            .collect(Collectors.groupingBy(ItsmWorkOrderManagerVo::getProcessingStatus, Collectors.counting()));
        log.info("统计工单总览中的左边部分接口，分组统计结果:{}", JSON.toJSONString(statusCountMap,true));

        // 已办结工单数
        long finishedCount = statusCountMap.getOrDefault("finish", 0L);

        // 审核中工单数
        long inReviewCount = statusCountMap.getOrDefault("inReview", 0L);

        // 使用一次流操作来获取紧急级别的工单数
        Map<String, Long> emergencyCountMap = itsmWorkOrderManagerVos.stream()
            .collect(Collectors.groupingBy(ItsmWorkOrderManagerVo::getEmergency, Collectors.counting()));

        // 正常工单数
        long normalCount = emergencyCountMap.getOrDefault("0", 0L);

        // 紧急工单数
        long urgentCount = emergencyCountMap.getOrDefault("1", 0L);

        // 警告工单数
        long warningCount = emergencyCountMap.getOrDefault("2", 0L);

        // 次要工单数
        long secondaryCount = emergencyCountMap.getOrDefault("3", 0L);

        // 计算办结率（保留两位小数）
        double settlementRate = total > 0 ? (finishedCount * 100.0) / total : 0.0;

        return ItsmWorkOrderTrendCountVo.builder()
            .total(total)
            .processing(finishedCount)
            .completed(inReviewCount)
            .completionRate(Math.round(settlementRate * 100) / 100.0) // 四舍五入保留两位小数
            .alertDistribution(ItsmWorkOrderTrendCountVo.AlertDistribution.builder()
                .emergencyNum(urgentCount)
                .principalNum(normalCount)
                .secondaryNum(secondaryCount)
                .warnNum(warningCount)
                .build())
            .build();
    }

    private List<ItsmSystemInfoVo> getSystemInfoList() {
        return TenantHelper.ignore(() -> itsmSystemInfoService.queryList(new ItsmSystemInfoBo()));
    }

    private List<String> extractSystemIds(List<ItsmSystemInfoVo> systemInfoVos) {
        return systemInfoVos.stream().map(ItsmSystemInfoVo::getSystemId).distinct().toList();
    }

    private List<ItsmServerInfoVo> getServerInfoList(List<String> systemIds) {
        return TenantHelper.ignore(() -> itsmServerInfoService.queryList(new ItsmServerInfoBo().setSystemIds(systemIds)));
    }

    private Map<String, List<ItsmServerInfoVo>> groupServersByIPv4(List<ItsmServerInfoVo> serverInfoVos) {
        return serverInfoVos.stream()
            .filter(vo -> StringUtils.isNotBlank(vo.getCloudServerIpv4()))
            .collect(Collectors.groupingBy(ItsmServerInfoVo::getCloudServerIpv4));
    }

    private Map<String, HealthStatus> checkServerHealth(Set<String> ipList) {
        return serverHealthCheckService.checkServerHealth(new ArrayList<>(ipList));
    }

    private ItsmCountSystemVo buildCountSystemVo(List<ItsmSystemInfoVo> systemInfoVos, List<String> systemIds,
                                            Map<String, List<ItsmServerInfoVo>> ipv4ServerMap,
                                            Map<String, HealthStatus> healthStatusMap) {
        long totalSystems = systemInfoVos.size();
        long noIPv4Systems = systemIds.size() - ipv4ServerMap.size();
        long unhealthySystems = countUnhealthySystems(ipv4ServerMap, healthStatusMap);
        long healthyServers = countHealthyServers(ipv4ServerMap, healthStatusMap);
        long unhealthyServers = ipv4ServerMap.size() - healthyServers;

        return ItsmCountSystemVo.builder()
            .totalSystems(totalSystems)
            .totalServers((long) ipv4ServerMap.size())
            .healthySystems(totalSystems - unhealthySystems - noIPv4Systems)
            .unhealthySystems(unhealthySystems)
            .healthyServers(healthyServers)
            .unhealthyServers(unhealthyServers)
            .noIPv4Systems(noIPv4Systems)
            .build();
    }

    private long countUnhealthySystems(Map<String, List<ItsmServerInfoVo>> ipv4ServerMap,
                                  Map<String, HealthStatus> healthStatusMap) {
        return ipv4ServerMap.entrySet().stream()
            .filter(entry -> {
                HealthStatus status = healthStatusMap.get(entry.getKey());
                return status != null && !status.isHealthy();
            })
            .mapToLong(entry -> entry.getValue().size())
            .sum();
    }

    private long countHealthyServers(Map<String, List<ItsmServerInfoVo>> ipv4ServerMap,
                                Map<String, HealthStatus> healthStatusMap) {
        return ipv4ServerMap.keySet().stream()
            .filter(ip -> {
                HealthStatus status = healthStatusMap.get(ip);
                return status != null && status.isHealthy();
            }).count();
    }

    private ItsmCountSystemVo buildEmptyCountSystemVo() {
        return ItsmCountSystemVo.builder()
            .healthySystems(0L)
            .totalServers(0L)
            .totalSystems(0L)
            .unhealthySystems(0L)
            .healthyServers(0L)
            .unhealthyServers(0L)
            .noIPv4Systems(0L)
            .build();
    }

    private ItsmWorkOrderTrendCountVo buildEmptyWorkOrderTrendCountVo() {
        return ItsmWorkOrderTrendCountVo.builder()
            .total(0L)
            .processing(0L)
            .completed(0L)
            .completionRate(0.0)
            .alertDistribution(ItsmWorkOrderTrendCountVo.AlertDistribution.builder()
                .emergencyNum(0L)
                .principalNum(0L)
                .secondaryNum(0L)
                .warnNum(0L)
                .build())
            .build();
    }

    /**
     * 计算最近6天的日期范围
     */
    private DateRange calculateDateRange() {
        LocalDate today = LocalDate.now();
        LocalDate startDate = today.minusDays(5); // 往前推5天

        // 准备日期标签
        List<String> dateLabels = new ArrayList<>(6);
        List<LocalDate> dates = new ArrayList<>(6);

        // 生成日期范围（从最早到最近）
        for (int i = 5; i >= 0; i--) {
            LocalDate date = today.minusDays(i);
            dates.add(date);
            dateLabels.add(formatDate(date));
        }

        // 转换为查询用的时间范围
        Date startDateTime = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDateTime = Date.from(today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        log.info("工单趋势统计时间范围：{} 至 {}", startDateTime, endDateTime);

        return new DateRange(startDateTime, endDateTime, dates, dateLabels);
    }

    /**
     * 格式化日期为 "M-d" 格式
     */
    private String formatDate(LocalDate date) {
        return date.format(DateTimeFormatter.ofPattern("M-d"));
    }

    /**
     * 查询指定日期范围内的工单
     */
    private List<ItsmWorkOrderManagerVo> queryWorkOrdersInDateRange(DateRange dateRange,ItsmWorkOrderManagerBo bo) {
        bo.setParams(Map.of(
            "beginCreateTime", dateRange.getStartDateTime(),
            "endCreateTime", dateRange.getEndDateTime()
        ));
        return TenantHelper.ignore(() -> itsmWorkOrderManagerService.queryList(bo));
    }

    /**
     * 按日期和紧急程度对工单进行分组统计
     */
    private Map<LocalDate, Map<String, Long>> aggregateWorkOrdersByDateAndEmergency(List<ItsmWorkOrderManagerVo> workOrders) {
        return workOrders.stream()
            .filter(this::isValidWorkOrder)
            .collect(Collectors.groupingBy(
                this::extractLocalDate,
                Collectors.groupingBy(
                    vo -> StringUtils.defaultIfBlank(vo.getEmergency(), "0"),
                    Collectors.counting()
                )
            ));
    }

    /**
     * 检查工单数据是否有效
     */
    private boolean isValidWorkOrder(ItsmWorkOrderManagerVo vo) {
        return vo.getCreateTime() != null;
    }

    /**
     * 从工单中提取日期
     */
    private LocalDate extractLocalDate(ItsmWorkOrderManagerVo vo) {
        return vo.getCreateTime().toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDate();
    }

    /**
     * 构建工单趋势视图对象
     */
    private WorkOrderTrendVo buildWorkOrderTrendVo(List<String> dateLabels, Map<LocalDate, Map<String, Long>> dailyStats) {
        // 准备四种紧急程度的数据系列
        List<SeriesDataVo> normalSeries = new ArrayList<>();     // 正常 - "0"
        List<SeriesDataVo> emergencySeries = new ArrayList<>();  // 紧急 - "1"
        List<SeriesDataVo> warnSeries = new ArrayList<>();       // 警告 - "2"
        List<SeriesDataVo> secondarySeries = new ArrayList<>();  // 次要 - "3"

        // 获取日期范围内的所有日期
        List<LocalDate> dateRange = new ArrayList<>(dailyStats.keySet());
        Collections.sort(dateRange);

        // 为每个日期填充数据
        for (int i = 0; i < dateLabels.size(); i++) {
            String dateLabel = dateLabels.get(i);
            LocalDate date = LocalDate.now().minusDays(5 - i);
            Map<String, Long> emergencyCounts = dailyStats.getOrDefault(date, Collections.emptyMap());

            // 为每种紧急程度创建数据点
            normalSeries.add(createDataPoint(dateLabel, emergencyCounts.getOrDefault("0", 0L)));
            emergencySeries.add(createDataPoint(dateLabel, emergencyCounts.getOrDefault("1", 0L)));
            warnSeries.add(createDataPoint(dateLabel, emergencyCounts.getOrDefault("2", 0L)));
            secondarySeries.add(createDataPoint(dateLabel, emergencyCounts.getOrDefault("3", 0L)));
        }

        // 构建并返回结果对象
        return WorkOrderTrendVo.builder()
            .usageRateXAxis(dateLabels)
            .normalSeries(normalSeries)
            .emergencySeries(emergencySeries)
            .warnSeries(warnSeries)
            .secondarySeries(secondarySeries)
            .build();
    }

    /**
     * 创建数据点
     */
    private SeriesDataVo createDataPoint(String date, Long count) {
        return new SeriesDataVo(date, String.valueOf(count));
    }

    /**
     * 构建空的工单趋势视图对象
     */
    private WorkOrderTrendVo buildEmptyWorkOrderTrendVo() {
        DateRange dateRange = calculateDateRange();
        List<String> dateLabels = dateRange.getDateLabels();

        // 创建空的数据系列
        List<SeriesDataVo> emptySeries = dateLabels.stream()
            .map(date -> createDataPoint(date, 0L))
            .collect(Collectors.toList());

        // 返回空的趋势图数据
        return WorkOrderTrendVo.builder()
            .usageRateXAxis(dateLabels)
            .normalSeries(new ArrayList<>(emptySeries))
            .emergencySeries(new ArrayList<>(emptySeries))
            .warnSeries(new ArrayList<>(emptySeries))
            .secondarySeries(new ArrayList<>(emptySeries))
            .build();
    }

    /**
     * 日期范围数据类
     */
    @Data
    @AllArgsConstructor
    private static class DateRange {
        private Date startDateTime;
        private Date endDateTime;
        private List<LocalDate> dates;
        private List<String> dateLabels;
    }

    /**
     * 将Date类型转换为LocalDateTime类型
     *
     * @param date Date对象
     * @return LocalDateTime对象，如果输入为null则返回null
     */
    private LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDateTime();
    }
}
