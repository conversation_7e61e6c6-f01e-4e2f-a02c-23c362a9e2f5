package org.dromara.itsm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.CopyUtil;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.itsm.domain.ItsmProjectUser;
import org.dromara.itsm.domain.ItsmServerInfo;
import org.dromara.itsm.domain.ItsmSystemInfo;
import org.dromara.itsm.domain.bo.*;
import org.dromara.itsm.domain.vo.*;
import org.dromara.itsm.mapper.ItsmProjectUserMapper;
import org.dromara.itsm.service.*;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.service.ISysUserService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 项目人员信息Service业务层处理
 *
 * <AUTHOR> Xu
 * @date 2024-10-16
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ItsmProjectUserServiceImpl implements IItsmProjectUserService {

    private final ItsmProjectUserMapper baseMapper;

    private final ISysUserService userService;

    private final IItsmProjectInfoService projectInfoService;

    private final IItsmSystemInfoService systemInfoService;

    private final IItsmServerInfoService serverInfoService;

    private final IItsmAttendanceLeaveLogService leaveLogService;

    @Override
    public ItsmProjectInfoVo queryPeopleNumById(String projectId) {
        ItsmProjectInfoVo projectInfoVo = TenantHelper.ignore(() -> projectInfoService.queryById(projectId));
        if (ObjectUtil.isNotNull(projectInfoVo)) {
            // 获取项目人员
            List<ItsmProjectUserVo> projectUserVos = TenantHelper.ignore(() -> baseMapper.selectVoList(
                Wrappers.lambdaQuery(ItsmProjectUser.class)
                    .eq(ItsmProjectUser::getProjectId, projectId)
                    .orderByDesc(ItsmProjectUser::getCreateTime)
            ));
            if (CollectionUtil.isNotEmpty(projectUserVos)) {
                // 类型转换添加异常处理（需在convertStringListToLongList实现）
                List<String> projectUserIds = projectUserVos.stream()
                    .map(ItsmProjectUserVo::getSysUserId)
                    .distinct().toList();
                List<SysUserVo> userVos = TenantHelper.ignore(() ->
                    userService.selectUserByIds_v1(convertStringListToLongList_v1(projectUserIds)));

                // 优化点：使用分区替代两次过滤
                Map<Boolean, List<SysUserVo>> userStatusMap = Optional.ofNullable(userVos)
                    .orElseGet(ArrayList::new)
                    .stream()
                    .collect(Collectors.partitioningBy(user -> "0".equals(user.getStatus())));

                long enableCount = userStatusMap.get(true).size();//预案在岗人数统计逻辑，暂时弃用，改用项目用户对象中是否在岗字段
                long noEnableCount = userStatusMap.get(false).size();

                // 优化点：提前计算时间区间
                Date currentDate = new Date();
                // 优化点：使用Optional包装可能为空的集合
                // 修改后逻辑（添加时间区间）
                Date now = new Date();
                Date searchStartTime = DateUtil.beginOfDay(DateUtil.offsetDay(now, -2));  // 三天前零点
                Date searchEndTime = DateUtil.endOfDay(now);  // 当前时间当天23:59:59

                long validLeaveCount = Optional.ofNullable(TenantHelper.ignore(() ->
                        leaveLogService.queryList(new ItsmAttendanceLeaveLogBo()
                            .setSysUserIds(projectUserIds)
                            .setSearchStartTime(searchStartTime)
                            .setSearchEndTime(searchEndTime))))
                    .orElseGet(ArrayList::new)
                    .stream()
                    .filter(log -> log.getStartTime() != null && log.getEndTime() != null)
                    .collect(Collectors.groupingBy(ItsmAttendanceLeaveLogVo::getSysUserId))
                    .values().stream()
                    .filter(logs -> logs.stream().anyMatch(log ->
                        DateUtil.isIn(currentDate,
                            DateUtil.beginOfDay(log.getStartTime()),
                            DateUtil.endOfDay(log.getEndTime()))))
                    .count();

                // 设置统计结果
                projectInfoVo.setProjectPeopleTotal((long) projectUserVos.size());
                //projectInfoVo.setProjectPeopleOnDutyNum(enableCount);
                //改成直接拿项目用户表是否在岗字段总数
                // 统计在岗人数（atWorkFlag为"1"的人数）
                long onDutyCount = projectUserVos.stream()
                    .filter(user -> "Y".equals(user.getAtWorkFlag()))
                    .count();
                projectInfoVo.setProjectPeopleOnDutyNum(onDutyCount);
                projectInfoVo.setProjectPeopleAbnormalNum(noEnableCount);
                projectInfoVo.setProjectPeopleLeaveNum(validLeaveCount);
            } else {
                projectInfoVo.setProjectPeopleTotal(0L);
                projectInfoVo.setProjectPeopleOnDutyNum(0L);
                projectInfoVo.setProjectPeopleLeaveNum(0L);
                projectInfoVo.setProjectPeopleAbnormalNum(0L);
            }
            return projectInfoVo;
        }
        return null;
    }

    @Override
    public ItsmProjectUserCountInfoVo getProjectUserCountInfo(String projectId) {
        if (StringUtils.isNotBlank(projectId)){
            ItsmProjectInfoVo projectInfoVo = TenantHelper.ignore(() -> projectInfoService.queryById(projectId));
            if (ObjectUtil.isNull(projectInfoVo)) {
                log.error("移动端获取人员数据信息，根据项目id：{}查询项目信息为空",projectId);
                return null;
            }
        }
        // 获取项目人员
        List<ItsmProjectUserVo> projectUserVos = TenantHelper.ignore(() -> baseMapper.selectVoList(
            Wrappers.lambdaQuery(ItsmProjectUser.class)
                .eq(StringUtils.isNotBlank(projectId),ItsmProjectUser::getProjectId, projectId)
                .orderByDesc(ItsmProjectUser::getCreateTime)
        ));
        if (CollectionUtil.isNotEmpty(projectUserVos)){
            ItsmProjectUserCountInfoVo projectUserCountInfoVo = new ItsmProjectUserCountInfoVo();
            if (CollectionUtil.isNotEmpty(projectUserVos)) {
                // 类型转换添加异常处理（需在convertStringListToLongList实现）
                List<String> projectUserIds = projectUserVos.stream()
                    .map(ItsmProjectUserVo::getSysUserId)
                    .distinct().toList();
                List<SysUserVo> userVos = TenantHelper.ignore(() ->
                    userService.selectUserByIds_v1(convertStringListToLongList_v1(projectUserIds)));

                // 优化点：使用分区替代两次过滤
                Map<Boolean, List<SysUserVo>> userStatusMap = Optional.ofNullable(userVos)
                    .orElseGet(ArrayList::new)
                    .stream()
                    .collect(Collectors.partitioningBy(user -> "0".equals(user.getStatus())));

                long enableCount = userStatusMap.get(true).size();//预案在岗人数统计逻辑，暂时弃用，改用项目用户对象中是否在岗字段
                long noEnableCount = userStatusMap.get(false).size();

                // 优化点：提前计算时间区间
                Date currentDate = new Date();
                // 优化点：使用Optional包装可能为空的集合
                // 修改后逻辑（添加时间区间）
                Date now = new Date();
                Date searchStartTime = DateUtil.beginOfDay(DateUtil.offsetDay(now, -2));  // 三天前零点
                Date searchEndTime = DateUtil.endOfDay(now);  // 当前时间当天23:59:59

                long validLeaveCount = Optional.ofNullable(TenantHelper.ignore(() ->
                        leaveLogService.queryList(new ItsmAttendanceLeaveLogBo()
                            .setSysUserIds(projectUserIds)
                            .setSearchStartTime(searchStartTime)
                            .setSearchEndTime(searchEndTime))))
                    .orElseGet(ArrayList::new)
                    .stream()
                    .filter(log -> log.getStartTime() != null && log.getEndTime() != null)
                    .collect(Collectors.groupingBy(ItsmAttendanceLeaveLogVo::getSysUserId))
                    .values().stream()
                    .filter(logs -> logs.stream().anyMatch(log ->
                        DateUtil.isIn(currentDate,
                            DateUtil.beginOfDay(log.getStartTime()),
                            DateUtil.endOfDay(log.getEndTime()))))
                    .count();

                // 设置统计结果
                projectUserCountInfoVo.setProjectPeopleTotal((long) projectUserVos.size());
                //projectInfoVo.setProjectPeopleOnDutyNum(enableCount);
                //改成直接拿项目用户表是否在岗字段总数
                // 统计在岗人数（atWorkFlag为"1"的人数）
                long onDutyCount = projectUserVos.stream()
                    .filter(user -> "Y".equals(user.getAtWorkFlag()))
                    .count();
                projectUserCountInfoVo.setProjectPeopleOnDutyNum(onDutyCount);
                projectUserCountInfoVo.setProjectPeopleAbnormalNum(noEnableCount);
                projectUserCountInfoVo.setProjectPeopleLeaveNum(validLeaveCount);
            } else {
                projectUserCountInfoVo.setProjectPeopleTotal(0L);
                projectUserCountInfoVo.setProjectPeopleOnDutyNum(0L);
                projectUserCountInfoVo.setProjectPeopleLeaveNum(0L);
                projectUserCountInfoVo.setProjectPeopleAbnormalNum(0L);
            }
            return projectUserCountInfoVo;
        }
        return null;
    }

    // 需补充的类型转换方法（示例实现）
    private List<Long> convertStringListToLongList_v1(List<String> stringList) {
        return Optional.ofNullable(stringList)
            .orElseGet(ArrayList::new)
            .stream()
            .map(str -> {
                try {
                    return Long.parseLong(str);
                } catch (NumberFormatException e) {
                    // 根据业务需求处理无效值，示例返回null后续过滤
                    return null;
                }
            })
            .filter(Objects::nonNull)
            .toList();
    }

    @Override
    public ItsmProjectInfoVo queryPeopleNumById_old(String projectId) {
        ItsmProjectInfoVo projectInfoVo = TenantHelper.ignore(() -> projectInfoService.queryById(projectId));
        if (ObjectUtil.isNotNull(projectInfoVo)){
            //获取项目人员
            List<ItsmProjectUserVo> projectUserVos = TenantHelper.ignore(() -> baseMapper.selectVoList(
                Wrappers.lambdaQuery(ItsmProjectUser.class)
                    .eq(ItsmProjectUser::getProjectId, projectId)
                    .orderByDesc(ItsmProjectUser::getCreateTime)
            ));
            if (CollectionUtil.isNotEmpty(projectUserVos)){
                //获取人员信息
                List<String> projectUserIds = projectUserVos.stream().map(ItsmProjectUserVo::getSysUserId)
                    .distinct().toList();
                List<SysUserVo> userVos = TenantHelper.ignore(() ->
                    userService.selectUserByIds_v1(convertStringListToLongList(projectUserIds)));
                List<SysUserVo> enableUsers = userVos.stream()
                    .filter(user -> Objects.equals("0", user.getStatus()))
                    .toList();
                List<SysUserVo> noEnableUsers = userVos.stream()
                    .filter(user -> Objects.equals("1", user.getStatus()))
                    .toList();
                // 在queryPeopleNumById方法中修改请假人数统计部分
                List<ItsmAttendanceLeaveLogVo> leaveLogVos = TenantHelper.ignore(() ->
                    leaveLogService.queryList(new ItsmAttendanceLeaveLogBo().setSysUserIds(projectUserIds)));

                // 使用hutool处理当前日期（精确到天）
                Date currentDate = DateUtil.beginOfDay(new Date());

                // 按用户分组并统计有效请假人数
                long validLeaveCount = leaveLogVos.stream()
                    // 过滤无效时间数据
                    .filter(log -> log.getStartTime() != null && log.getEndTime() != null)
                    // 按用户分组
                    .collect(Collectors.groupingBy(ItsmAttendanceLeaveLogVo::getSysUserId))
                    // 处理分组后的数据
                    .entrySet().stream()
                    // 过滤出有有效请假记录的用户
                    .filter(entry -> entry.getValue().stream()
                        .anyMatch(log ->
                            DateUtil.isIn(currentDate,
                                DateUtil.beginOfDay(log.getStartTime()),
                                DateUtil.endOfDay(log.getEndTime()))
                        )
                    )
                    // 统计人数
                    .count();

                // 设置统计结果
                projectInfoVo.setProjectPeopleLeaveNum(validLeaveCount);

                projectInfoVo.setProjectPeopleTotal((long) projectUserVos.size());
                projectInfoVo.setProjectPeopleOnDutyNum((long)enableUsers.size());
                projectInfoVo.setProjectPeopleAbnormalNum((long)noEnableUsers.size());
            }else {
                projectInfoVo.setProjectPeopleTotal(0L);
                projectInfoVo.setProjectPeopleOnDutyNum(0L);
                projectInfoVo.setProjectPeopleLeaveNum(0L);
                projectInfoVo.setProjectPeopleAbnormalNum(0L);
            }
            return projectInfoVo;
        }
        return null;
    }

    // 通用的租户查询模板
    private <T> T tenantQuery(Supplier<T> querySupplier) {
        return LoginHelper.isSuperAdmin()
            ? TenantHelper.ignore(querySupplier)
            : querySupplier.get();
    }

    // 条件构建策略接口
    @FunctionalInterface
    private interface QueryConditionStrategy {
        void apply(LambdaQueryWrapper<ItsmProjectUser> wrapper, ItsmProjectUserBo bo);
    }

    // 默认条件构建策略
    private final QueryConditionStrategy DEFAULT_CONDITION_STRATEGY = (wrapper, bo) -> {
        // 条件映射
        Map<Function<ItsmProjectUserBo, Object>, BiConsumer<LambdaQueryWrapper<ItsmProjectUser>, Object>> conditionMap =
            new HashMap<>() {{
                put(ItsmProjectUserBo::getSysUserId, (w, v) -> w.eq(ItsmProjectUser::getSysUserId, v));
                put(ItsmProjectUserBo::getProjectId, (w, v) -> w.eq(ItsmProjectUser::getProjectId, v));
                put(ItsmProjectUserBo::getSystemId, (w, v) -> w.eq(ItsmProjectUser::getSystemId, v));
                put(ItsmProjectUserBo::getIdCard, (w, v) -> w.eq(ItsmProjectUser::getIdCard, v));
                put(ItsmProjectUserBo::getAtWorkFlag, (w, v) -> w.eq(ItsmProjectUser::getAtWorkFlag, v));
                put(ItsmProjectUserBo::getServiceBeginDate, (w, v) -> w.eq(ItsmProjectUser::getServiceBeginDate, v));
                put(ItsmProjectUserBo::getUserCompanyName, (w, v) -> w.like(ItsmProjectUser::getUserCompanyName, v));
                put(ItsmProjectUserBo::getPosition, (w, v) -> w.eq(ItsmProjectUser::getPosition, v));
                put(ItsmProjectUserBo::getPoliticsStatus, (w, v) -> w.eq(ItsmProjectUser::getPoliticsStatus, v));
                put(ItsmProjectUserBo::getProjectIds, (w, v) -> w.in(ItsmProjectUser::getProjectId, v));
                put(ItsmProjectUserBo::getSystemIds, (w, v) -> w.in(ItsmProjectUser::getSystemId, v));
            }};

        // 动态应用条件
        conditionMap.forEach((getter, consumer) -> {
            Object value = getter.apply(bo);
            if (value != null) {
                consumer.accept(wrapper, value);
            }
        });

        // 排序和部门过滤
        wrapper.orderByDesc(ItsmProjectUser::getCreateTime);
        Optional.ofNullable(bo.getCreateDept())
            .ifPresent(dept -> wrapper.eq(BaseEntity::getCreateDept, dept));
    };

    // 通用查询方法
    private <T> List<T> commonQuery(
        Function<LambdaQueryWrapper<ItsmProjectUser>, List<T>> queryFunc,
        ItsmProjectUserBo bo
    ) {
        return commonQuery(queryFunc, bo, DEFAULT_CONDITION_STRATEGY);
    }

    // 重载的通用查询方法，支持自定义策略
    private <T> List<T> commonQuery(
        Function<LambdaQueryWrapper<ItsmProjectUser>, List<T>> queryFunc,
        ItsmProjectUserBo bo,
        QueryConditionStrategy strategy
    ) {
        LambdaQueryWrapper<ItsmProjectUser> wrapper = Wrappers.lambdaQuery();

        // 应用条件策略
        if (strategy != null) {
            strategy.apply(wrapper, bo);
        }

        return tenantQuery(() -> queryFunc.apply(wrapper));
    }

    /**
     * 查询项目人员信息
     *
     * @param userId 主键
     * @return 项目人员信息
     */
    @Override
    public ItsmProjectUserVo queryById(String userId) {
        return tenantQuery(() -> baseMapper.selectVoById(userId));
    }

    /**
     * 分页查询项目人员信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目人员信息分页列表
     */
    @Override
    public TableDataInfo<ItsmProjectUserVo> queryPageList(ItsmProjectUserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ItsmProjectUser> wrapper = Wrappers.lambdaQuery();
        DEFAULT_CONDITION_STRATEGY.apply(wrapper, bo);

        Page<ItsmProjectUserVo> result = tenantQuery(() -> {
            if (LoginHelper.isTenantProjectAdmin(LoginHelper.getLoginUser().getRolePermission())) {
                return baseMapper.selectVoPage(pageQuery.build(), wrapper);
            }
            bo.setCreateDept(LoginHelper.getDeptId());
            return baseMapper.selectVoPage(pageQuery.build(), wrapper);
        });

        enrichProjectUserData(result);
        return TableDataInfo.build(result);
    }

    @Override
    public TableDataInfo<ItsmProjectUserVo> queryMonitorPageList(ItsmProjectUserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ItsmProjectUser> wrapper = Wrappers.lambdaQuery();
        DEFAULT_CONDITION_STRATEGY.apply(wrapper, bo);
        Page<ItsmProjectUserVo> result = tenantQuery(() -> baseMapper.selectVoPage(pageQuery.build(), wrapper));
        enrichProjectUserMonitorData(result);
        return TableDataInfo.build(result);
    }

    // 数据丰富方法
    private void enrichProjectUserMonitorData(Page<?> result) {
        if (result == null || CollectionUtil.isEmpty(result.getRecords())) {
            return;
        }

        List<Long> sysUserIds = result.getRecords().stream()
            .map(vo -> {
                if (vo instanceof ItsmProjectUserVo) {
                    return Long.valueOf(((ItsmProjectUserVo) vo).getSysUserId());
                }
                return null;
            })
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(sysUserIds)) {
            return;
        }

        List<SysUserVo> sysUserVos = userService.selectMonitorUserByIds(sysUserIds, null);

        Map<Long, SysUserVo> sysUserMap = sysUserVos.stream().collect(Collectors.toMap(SysUserVo::getUserId, Function.identity()));
        result.getRecords().forEach(vo -> {
            if (vo instanceof ItsmProjectUserVo projectUserVo) {
                projectUserVo.setUserName(
                    Optional.ofNullable(sysUserMap.get(Long.valueOf(projectUserVo.getSysUserId())))
                        .map(SysUserVo::getNickName)
                        .orElse(null)
                );
            }
        });
    }

    // 数据丰富方法
    private void enrichProjectUserData(Page<?> result) {
        if (result == null || CollectionUtil.isEmpty(result.getRecords())) {
            return;
        }

        List<Long> sysUserIds = result.getRecords().stream()
            .map(vo -> {
                if (vo instanceof ItsmProjectUserVo) {
                    return Long.valueOf(((ItsmProjectUserVo) vo).getSysUserId());
                }
                return null;
            })
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(sysUserIds)) {
            return;
        }

        List<SysUserVo> sysUserVos = userService.selectUserByIds(sysUserIds,
            LoginHelper.isSuperAdmin() ? null : LoginHelper.getLoginUser().getDeptId());

        Map<Long, SysUserVo> sysUserMap = sysUserVos.stream()
            .collect(Collectors.toMap(SysUserVo::getUserId, Function.identity()));

        result.getRecords().forEach(vo -> {
            if (vo instanceof ItsmProjectUserVo projectUserVo) {
                projectUserVo.setUserName(
                    Optional.ofNullable(sysUserMap.get(Long.valueOf(projectUserVo.getSysUserId())))
                        .map(SysUserVo::getNickName)
                        .orElse(null)
                );
            }
        });
    }

    /**
     * 查询符合条件的项目人员信息列表
     *
     * @param bo 查询条件
     * @return 项目人员信息列表
     */
    @Override
    public List<ItsmProjectUserVo> queryList(ItsmProjectUserBo bo) {
        return commonQuery(baseMapper::selectVoList, bo);
    }

    @Override
    public List<ItsmProjectUserExcelVo> queryExcelList(ItsmProjectUserBo bo) {
        List<ItsmProjectUser> userInfos = commonQuery(baseMapper::selectList, bo);
        return processSystemInfos(userInfos);
    }

    private List<ItsmProjectUserExcelVo> processSystemInfos(List<ItsmProjectUser> itsmProjectUserInfos) {
        if (CollectionUtil.isEmpty(itsmProjectUserInfos)) {
            return Collections.emptyList();
        }
        List<ItsmProjectUserExcelBo> itsmProjectUserExcelBos
            = MapstructUtils.convert(itsmProjectUserInfos, ItsmProjectUserExcelBo.class);

        if (CollectionUtil.isNotEmpty(itsmProjectUserExcelBos)) {
            List<String> projectIds = itsmProjectUserExcelBos.stream()
                .map(ItsmProjectUserExcelBo::getProjectId)
                .distinct()
                .toList();
            List<String> systemIds = itsmProjectUserExcelBos.stream()
                .map(ItsmProjectUserExcelBo::getSystemId)
                .distinct()
                .toList();
            List<String> sysUserIds = itsmProjectUserExcelBos.stream()
                .map(ItsmProjectUserExcelBo::getSysUserId)
                .distinct()
                .toList();

            ItsmProjectInfoBo projectInfoBo = new ItsmProjectInfoBo().setProjectIds(projectIds);
            List<ItsmProjectInfoVo> projectInfoVos = projectInfoService.queryList(projectInfoBo);

            if (CollectionUtil.isNotEmpty(projectInfoVos)) {
                Map<String, ItsmProjectInfoVo> projectInfoMap = projectInfoVos.stream()
                    .collect(Collectors.toMap(ItsmProjectInfoVo::getProjectId, it -> it));

                itsmProjectUserExcelBos.forEach(excelBo -> {
                    ItsmProjectInfoVo projectInfoVo = projectInfoMap.get(excelBo.getProjectId());
                    excelBo.setProjectName(ObjectUtil.isNull(projectInfoVo) ? "" : projectInfoVo.getProjectName());
                });
            }

            ItsmSystemInfoBo systemInfoBo = new ItsmSystemInfoBo().setSystemIds(systemIds);
            List<ItsmSystemInfoVo> systemInfoVos = systemInfoService.queryList(systemInfoBo);
            if (CollectionUtil.isNotEmpty(systemInfoVos)){
                Map<String, ItsmSystemInfoVo> systemInfoMap = systemInfoVos.stream()
                    .collect(Collectors.toMap(ItsmSystemInfoVo::getSystemId, it -> it));
                itsmProjectUserExcelBos.forEach(excelBo -> {
                    ItsmSystemInfoVo itsmSystemInfoVo = systemInfoMap.get(excelBo.getSystemId());
                    excelBo.setSystemName(ObjectUtil.isNull(itsmSystemInfoVo) ? "" : itsmSystemInfoVo.getSystemName());
                });
            }

            List<Long> sysUserLongIds = convertStringListToLongList(sysUserIds);
            List<SysUserVo> sysUserVos = userService.selectUserByIds(sysUserLongIds, null);
            if (CollectionUtil.isNotEmpty(sysUserVos)) {
                Map<Long, SysUserVo> sysUserMap = sysUserVos.stream()
                    .collect(Collectors.toMap(SysUserVo::getUserId, sysUserVo -> sysUserVo));
                itsmProjectUserExcelBos.forEach(excelBo -> {
                    SysUserVo sysUserVo = sysUserMap.get(Long.valueOf(excelBo.getSysUserId()));
                    if (sysUserVo != null) {
                        excelBo.setUserName(sysUserVo.getNickName());
                    }
                });
            }
        }
        return MapstructUtils.convert(itsmProjectUserExcelBos, ItsmProjectUserExcelVo.class);
    }

    @Override
    public List<ProjectSysUserInfoVo> queryByProjectSysUser(String projectId, String systemId) {
        if (LoginHelper.isSuperAdmin()){
            return TenantHelper.ignore(() ->baseMapper.queryByProjectSysUser(projectId, systemId));
        }
        return baseMapper.queryByProjectSysUser(projectId, systemId);
    }

    @Override
    public List<ProjectSysUserInfoVo> queryMonitorByProjectSysUser(String projectId, String systemId) {
        return TenantHelper.ignore(() -> baseMapper.queryByProjectSysUser(projectId, systemId));
    }

    @Override
    public List<ProjectSysUserInfoVo> queryProjectSysUserByProjectAndSystemIds(String projectId, List<String> systemIds) {
        if (LoginHelper.isSuperAdmin()){
            return TenantHelper.ignore(() ->baseMapper.queryProjectSysUserByProjectAndSystemIds(projectId, systemIds));
        }
        return baseMapper.queryProjectSysUserByProjectAndSystemIds(projectId, systemIds);
    }

    /**
     * 新增项目人员信息
     *
     * @param bo 项目人员信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ItsmProjectUserBo bo) {
        ItsmProjectUser add = MapstructUtils.convert(bo, ItsmProjectUser.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setUserId(add.getUserId());
        }
        return flag;
    }

    /**
     * 修改项目人员信息
     *
     * @param bo 项目人员信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ItsmProjectUserBo bo) {
        ItsmProjectUser update = MapstructUtils.convert(bo, ItsmProjectUser.class);
        validEntityBeforeSave(update);
        return tenantQuery(() -> baseMapper.updateById(update) > 0);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ItsmProjectUser entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除项目人员信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<ItsmAllProjectInfoVo> getItsmAllProjectInfoVo() {
        // 根据用户角色决定是否忽略租户信息查询所有项目信息
        Supplier<List<ItsmProjectInfoVo>> projectInfoSupplier = LoginHelper.isSuperAdmin() ?
            () -> TenantHelper.ignore(() -> projectInfoService.queryList(new ItsmProjectInfoBo()))
            :() -> projectInfoService.queryList(new ItsmProjectInfoBo());

        List<ItsmAllProjectInfoVo> allProjectInfoVos = fetchAndProcessProjects(projectInfoSupplier);
        log.info("获取项目，系统，用户最终结果：{}",JSON.toJSONString(allProjectInfoVos,true));
        return allProjectInfoVos;
    }

    @Override
    public List<ItsmAllProjectInfoVo> getMonitorItsmAllProjectInfoVo() {
        List<ItsmAllProjectInfoVo> allProjectInfoVos
            = fetchAndProcessProjectsMonitor(TenantHelper.ignore(() -> projectInfoService.queryMonitorDataList(new ItsmProjectInfoBo())));
        log.info("数据大屏，获取项目，系统，用户最终结果：{}",JSON.toJSONString(allProjectInfoVos,true));
        return allProjectInfoVos;
    }

    private List<ItsmAllProjectInfoVo> fetchAndProcessProjects(Supplier<List<ItsmProjectInfoVo>> projectInfoSupplier) {
        List<ItsmProjectInfoVo> itsmProjectInfoVos = projectInfoSupplier.get();
        if (CollectionUtil.isEmpty(itsmProjectInfoVos)) {
            return Collections.emptyList();
        }

        List<ItsmAllProjectInfoVo> allProjectInfoVos = MapstructUtils.convert(itsmProjectInfoVos, ItsmAllProjectInfoVo.class);

        // 获取唯一的projectId列表，并查询对应的系统信息
        List<String> projectIds = allProjectInfoVos.stream()
            .map(ItsmAllProjectInfoVo::getProjectId)
            .distinct()
            .toList();

        List<ItsmSystemInfoVo> systemInfoVos = LoginHelper.isSuperAdmin()? TenantHelper.ignore(() ->
            systemInfoService.queryList(new ItsmSystemInfoBo().setProjectIds(projectIds))):
            systemInfoService.queryList(new ItsmSystemInfoBo().setProjectIds(projectIds));

        if (CollectionUtil.isNotEmpty(systemInfoVos)) {
            Map<String, List<ItsmSystemInfoVo>> projectIdToSystemInfoMap = convertToMapByProjectId(systemInfoVos);

            allProjectInfoVos.forEach(p -> {
                List<ItsmSystemInfoVo> projectSystems
                    = projectIdToSystemInfoMap.getOrDefault(p.getProjectId(), Collections.emptyList());
                //只有项目+人
                p.setUserInfoVos(queryByProjectSysUser(p.getProjectId(), ""));

                List<ItsmAllSystemInfoVo> allSystemInfoVos
                    = MapstructUtils.convert(projectSystems, ItsmAllSystemInfoVo.class);
                p.setSystemInfoVos(allSystemInfoVos);

                //项目+系统+人+服务器信息
                allSystemInfoVos.forEach(s ->{
                    s.setUserInfoVos(queryByProjectSysUser(p.getProjectId(), s.getSystemId()));
                    s.setServerInfoVos(MapstructUtils.convert(
                        serverInfoService.queryList(
                            new ItsmServerInfoBo().setSystemId(s.getSystemId())),ProjectSystemServerInfoVo.class));
                });
            });
        }
        return allProjectInfoVos;
    }

    private List<ItsmAllProjectInfoVo> fetchAndProcessProjectsMonitor(List<ItsmProjectInfoVo> itsmProjectInfoVos) {
        if (CollectionUtil.isEmpty(itsmProjectInfoVos)) {
            return Collections.emptyList();
        }

        List<ItsmAllProjectInfoVo> allProjectInfoVos = MapstructUtils.convert(itsmProjectInfoVos, ItsmAllProjectInfoVo.class);

        // 获取唯一的projectId列表，并查询对应的系统信息
        List<String> projectIds = allProjectInfoVos.stream()
            .map(ItsmAllProjectInfoVo::getProjectId)
            .distinct()
            .toList();

        List<ItsmSystemInfo> systemInfos = systemInfoService.lambdaQuery().in(ItsmSystemInfo::getProjectId, projectIds).list();
        List<ItsmSystemInfoVo> systemInfoVos = MapstructUtils.convert(systemInfos, ItsmSystemInfoVo.class);

        if (CollectionUtil.isNotEmpty(systemInfoVos)) {
            Map<String, List<ItsmSystemInfoVo>> projectIdToSystemInfoMap = convertToMapByProjectId(systemInfoVos);

            allProjectInfoVos.forEach(p -> {
                List<ItsmSystemInfoVo> projectSystems
                    = projectIdToSystemInfoMap.getOrDefault(p.getProjectId(), Collections.emptyList());
                //只有项目+人
                p.setUserInfoVos(queryMonitorByProjectSysUser(p.getProjectId(), ""));

                List<ItsmAllSystemInfoVo> allSystemInfoVos
                    = MapstructUtils.convert(projectSystems, ItsmAllSystemInfoVo.class);
                p.setSystemInfoVos(allSystemInfoVos);

                if (CollectionUtil.isNotEmpty(allSystemInfoVos)){
                    //项目+系统+人+服务器信息
                    allSystemInfoVos.forEach(s ->{
                        s.setUserInfoVos(queryMonitorByProjectSysUser(p.getProjectId(), s.getSystemId()));
                        List<ItsmServerInfo> serverInfos
                            = serverInfoService.lambdaQuery().eq(ItsmServerInfo::getSystemId, s.getSystemId()).list();
                        List<ProjectSystemServerInfoVo> serverInfoVos
                             = CopyUtil.copyList(serverInfos, ProjectSystemServerInfoVo.class);
                        s.setServerInfoVos(serverInfoVos);
                    });
                }
            });
        }
        return allProjectInfoVos;
    }

    // 辅助方法用于转换systemInfoVos为map形式
    private Map<String, List<ItsmSystemInfoVo>> convertToMapByProjectId(List<ItsmSystemInfoVo> systemInfoVos) {
        return systemInfoVos.stream()
            .collect(Collectors.groupingBy(ItsmSystemInfoVo::getProjectId));
    }

    /**
     * 将 List<String> 转换为 List<Long>
     *
     * @param stringList 输入的字符串列表
     * @return 转换后的长整型列表
     * @throws NumberFormatException 如果字符串无法转换为长整型
     */
    private List<Long> convertStringListToLongList(List<String> stringList) throws NumberFormatException {
        List<Long> longList = new ArrayList<>();
        for (String str : stringList) {
            longList.add(Long.parseLong(str));
        }
        return longList;
    }
}
