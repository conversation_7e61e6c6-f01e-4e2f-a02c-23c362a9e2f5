package org.dromara.itsm.snailjob;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import com.aizuda.snailjob.common.log.SnailJobLog;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.itsm.domain.bo.ItsmAttendanceRecordBo;
import org.dromara.itsm.domain.vo.ItsmAttendanceRecordVo;
import org.dromara.itsm.domain.vo.attendance.ItsmAttendanceSchedulingInfoVo;
import org.dromara.itsm.service.IItsmAttendanceRecordService;
import org.dromara.itsm.service.IItsmAttendanceSchedulingService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Time;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 处理每天凌晨1点分修改前一天数据
 */
@Component
@JobExecutor(name = "itsmAttendanceRecordStatusUpdateCountExecutor")
@RequiredArgsConstructor
@Slf4j
public class ItsmAttendanceRecordStatusUpdateCountExecutor {

    private final IItsmAttendanceRecordService recordService;

    private final IItsmAttendanceSchedulingService schedulingService;


    @Transactional
    public ExecuteResult jobExecute(JobArgs jobArgs) {
        logJobExecution(jobArgs);

        List<ItsmAttendanceRecordVo> recordVos
            = recordService.queryList(new ItsmAttendanceRecordBo().setAppSearchTime(DateUtil.yesterday()));
        log.info("itsmAttendanceRecordStatusUpdateCountExecutor -> recordVos:{}",JSON.toJSONString(recordVos,true));
        Map<String, ItsmAttendanceSchedulingInfoVo> schedulingInfoMap = fetchSchedulingInfo(recordVos);

        recordVos.forEach(r -> processRecord(r, schedulingInfoMap));

        return ExecuteResult.success("系统访问考勤记录同步执行器执行成功");
    }

    private void logJobExecution(JobArgs jobArgs) {
        String jobArgsJson = JSON.toJSONString(jobArgs, true);
        SnailJobLog.LOCAL.info("本地日志_系统访问考勤记录同步执行器. 任务参数:{}", jobArgsJson);
        SnailJobLog.REMOTE.info("远程日志_系统访问考勤记录同步执行器. 任务参数:{}", jobArgsJson);
    }

    private Map<String, ItsmAttendanceSchedulingInfoVo> fetchSchedulingInfo(List<ItsmAttendanceRecordVo> recordVos) {
        List<String> sysUserIds = recordVos.stream().map(ItsmAttendanceRecordVo::getSysUserId).distinct().toList();
        return schedulingService.getItsmAttendanceSchedulingInfoByUserIds(sysUserIds).stream()
            .collect(Collectors.toMap(ItsmAttendanceSchedulingInfoVo::getSysUserId, Function.identity(),
                (existing, replacement) -> existing));
    }

    private void processRecord(ItsmAttendanceRecordVo r, Map<String, ItsmAttendanceSchedulingInfoVo> schedulingInfoMap) {
        ItsmAttendanceSchedulingInfoVo schedulingInfoVo = schedulingInfoMap.get(r.getSysUserId());
        if (ObjectUtil.isNull(schedulingInfoVo)) {
            r.setStatus("");
            return;
        }

        evaluateAttendance(r, schedulingInfoVo);
    }

    private void evaluateAttendance(ItsmAttendanceRecordVo r, ItsmAttendanceSchedulingInfoVo schedulingInfoVo) {
        Time scheduledStartTime = schedulingInfoVo.getPunchInTime();
        Time scheduledEndTime = schedulingInfoVo.getPunchOutTime();
        LocalTime actualStartTime = r.getClockInTime();
        LocalTime actualEndTime = r.getClockOutTime();

        LocalTime scheduledStartLocalTime = scheduledStartTime.toLocalTime();
        LocalTime scheduledEndLocalTime = scheduledEndTime.toLocalTime();

        setAttendanceStatus(r, actualStartTime, actualEndTime, scheduledStartLocalTime, scheduledEndLocalTime);
    }

    private void setAttendanceStatus(ItsmAttendanceRecordVo r, LocalTime actualStartTime, LocalTime actualEndTime, LocalTime scheduledStartLocalTime, LocalTime scheduledEndLocalTime) {
        if (actualStartTime == null && actualEndTime == null) {
            r.setStatus(""); // 旷工，待处理
        } else if (actualStartTime == null) {
            r.setStatus(""); // 上班未打卡，待协商处理
        } else if (actualEndTime == null) {
            r.setStatus(""); // 下班未打卡，待协商处理
        } else {
            long startTimeDiff = ChronoUnit.MINUTES.between(scheduledStartLocalTime, actualStartTime);
            long endTimeDiff = ChronoUnit.MINUTES.between(actualEndTime, scheduledEndLocalTime);

            if (startTimeDiff > 0) {
                r.setStatus(endTimeDiff > 0 ? "5" : "2"); // 迟到且早退 或 仅迟到
            } else if (endTimeDiff > 0) {
                r.setStatus("6"); // 仅早退
            } else {
                r.setStatus("1"); // 正常
            }
        }
    }

    public static void main(String[] args) {
        System.out.println("输出一下昨天的日期格式：" + DateUtil.yesterday().toDateStr());
        System.out.println("输出一下昨天的日期开始格式：" + DateUtil.beginOfDay(DateUtil.yesterday()));
        System.out.println("输出一下昨天的日期结束格式：" + DateUtil.endOfDay(DateUtil.yesterday()));
    }
}
