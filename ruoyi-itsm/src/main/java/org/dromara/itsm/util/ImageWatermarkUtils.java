//package org.dromara.itsm.util;
//
////import javax.imageio.ImageIO;
////import java.awt.*;
////import java.awt.geom.AffineTransform;
////import java.awt.image.BufferedImage;
////import java.io.ByteArrayInputStream;
////import java.io.ByteArrayOutputStream;
////import java.io.IOException;
////
////public class ImageWatermarkUtils {
////    /**
////     * 为图片添加动态文本水印
////     * @param imageBytes   原始图片字节
////     * @param waterMarkText 水印文字
////     */
////    public static byte[] addTextWatermark(byte[] imageBytes, String waterMarkText) throws IOException {
////        BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(imageBytes));
////        Graphics2D g = originalImage.createGraphics();
////
////        // 设置水印样式
////        g.setFont(new Font("微软雅黑", Font.BOLD, 40));
////        g.setColor(new Color(255, 0, 0, 128)); // 半透明红色
////        g.rotate(Math.toRadians(-30));         // 倾斜30度
////
////        // 计算水印位置（居中）
////        int x = (originalImage.getWidth() - g.getFontMetrics().stringWidth(waterMarkText)) / 2;
////        int y = originalImage.getHeight() / 2;
////        g.drawString(waterMarkText, x, y);
////        g.dispose();
////
////        // 输出为字节流
////        ByteArrayOutputStream os = new ByteArrayOutputStream();
////        ImageIO.write(originalImage, "png", os);
////        return os.toByteArray();
////    }
////
////
////    /**
////     * 为图片添加平铺水印（支持倾斜和透明度）
////     * @param originalImage 原始图片
////     * @param watermarkText 水印文字
////     * @param angle 倾斜角度（如-30度）
////     * @param alpha 透明度（0.0-1.0）
////     * @param xInterval X轴间隔
////     * @param yInterval Y轴间隔
////     * @return 带水印的图片字节数组
////     */
////    public static byte[] addTiledWatermark(BufferedImage originalImage, String watermarkText,
////                                           float angle, float alpha, int xInterval, int yInterval) throws IOException {
////        Graphics2D g2d = originalImage.createGraphics();
////
////        // 设置抗锯齿和字体
////        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
////        g2d.setFont(new Font("微软雅黑", Font.BOLD, 40));
////        g2d.setColor(new Color(0, 0, 0, (int) (alpha * 255))); // 颜色可配置
////
////        // 动态平铺水印
////        int imgWidth = originalImage.getWidth();
////        int imgHeight = originalImage.getHeight();
////        for (int x = 0; x < imgWidth; x += xInterval) {
////            for (int y = 0; y < imgHeight; y += yInterval) {
////                drawWatermark(g2d, x, y, watermarkText, angle, alpha);
////            }
////        }
////
////        g2d.dispose();
////
////        // 输出为字节流
////        ByteArrayOutputStream os = new ByteArrayOutputStream();
////        ImageIO.write(originalImage, "png", os);
////        return os.toByteArray();
////    }
////
////    private static void drawWatermark(Graphics2D g2d, int x, int y, String text, float angle, float alpha) {
////        AffineTransform originalTransform = g2d.getTransform();
////        AlphaComposite alphaComposite = AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha);
////        g2d.setComposite(alphaComposite);
////        g2d.rotate(Math.toRadians(angle), x, y);
////        g2d.drawString(text, x, y);
////        g2d.setTransform(originalTransform);
////    }
////}
//
//
//import net.coobird.thumbnailator.Thumbnails;
//
//import javax.imageio.ImageIO;
//import java.awt.*;
//import java.awt.geom.AffineTransform;
//import java.awt.image.BufferedImage;
//import java.io.ByteArrayInputStream;
//import java.io.ByteArrayOutputStream;
//
//public class ImageWatermarkUtils {
//
//    /**
//     * 添加平铺水印（支持透明度、倾斜角度）
//     * @param imageBytes 原始图片字节
//     * @param text       水印文字
//     * @param alpha      透明度（0.0-1.0）
//     * @param angle      倾斜角度（如-30）
//     * @param intervalX  X轴间隔
//     * @param intervalY  Y轴间隔
//     * @return 带水印的图片字节数组
//     */
//    public static byte[] addTiledWatermark(byte[] imageBytes, String text,
//                                           float alpha, double angle,
//                                           int intervalX, int intervalY) throws Exception {
//        // 1. 读取原始图片
//        BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(imageBytes));
//        int width = originalImage.getWidth();
//        int height = originalImage.getHeight();
//
//        // 2. 创建画布并绘制原图
//        BufferedImage watermarkedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
//        Graphics2D g2d = watermarkedImage.createGraphics();
//        g2d.drawImage(originalImage, 0, 0, null);
//
//        // 3. 设置水印样式
//        g2d.setFont(new Font("微软雅黑", Font.BOLD, 40));
//        g2d.setColor(new Color(255, 0, 0, (int) (alpha * 255))); // 半透明红色
//        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
//
//        // 4. 平铺水印（循环绘制）
//        AffineTransform originalTransform = g2d.getTransform();
//        for (int x = -width; x < width * 2; x += intervalX) {
//            for (int y = -height; y < height * 2; y += intervalY) {
//                // 设置旋转和透明度
//                g2d.rotate(Math.toRadians(angle), x, y);
//                g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha));
//                // 绘制水印文字
//                g2d.drawString(text, x, y);
//                // 恢复原始旋转状态
//                g2d.setTransform(originalTransform);
//            }
//        }
//        g2d.dispose();
//
//        // 5. 输出为字节流
//        ByteArrayOutputStream os = new ByteArrayOutputStream();
//        Thumbnails.of(watermarkedImage)
//            .scale(1.0) // 保持原尺寸
//            .outputFormat("png")
//            .outputQuality(1.0)
//            .toOutputStream(os);
//        return os.toByteArray();
//    }
//}
