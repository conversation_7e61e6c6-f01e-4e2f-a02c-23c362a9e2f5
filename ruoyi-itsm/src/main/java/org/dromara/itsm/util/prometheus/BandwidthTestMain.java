package org.dromara.itsm.util.prometheus;

import org.dromara.itsm.util.prometheus.dto.BandwidthData;
import org.dromara.itsm.util.prometheus.dto.BandwidthStatistics;
import org.dromara.itsm.util.prometheus.dto.RealTimeBandwidthResult;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Scanner;

/**
 * 实时带宽查询测试主类
 *
 * 包含多个测试用例，用于验证实时带宽查询功能的正确性
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
public class BandwidthTestMain {

    // 测试服务器配置（请根据实际情况修改）
    private static final String TEST_IP = "*************";
    private static final String TEST_PORT = "9100";

    public static void main(String[] args) {
        System.out.println("===== 实时带宽查询功能测试 =====");
        System.out.println("测试服务器：" + TEST_IP + ":" + TEST_PORT);
        System.out.println("开始时间：" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        System.out.println();

        try {
            // 测试用例1：查询最近1小时的上传带宽
            testUploadBandwidth();

            // 测试用例2：查询最近1小时的下载带宽
            testDownloadBandwidth();

            // 测试用例3：查询指定时间段的完整带宽数据
            testSpecificTimeRangeBandwidth();

            // 测试用例4：查询最近3小时的带宽数据（使用便捷方法）
            testConvenienceMethod();

            // 测试用例5：交互式测试（可选）
            interactiveTest();

        } catch (Exception e) {
            System.err.println("测试过程中发生异常：" + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("\n===== 所有测试完成 =====");
    }

    /**
     * 测试用例1：查询最近1小时的上传带宽
     */
    public static void testUploadBandwidth() {
        System.out.println("【测试用例1】查询最近1小时的上传带宽");
        System.out.println("----------------------------------------");

        try {
            // 计算时间范围：最近1小时
            long currentTime = System.currentTimeMillis() / 1000;
            long startTime = currentTime - 3600; // 1小时前
            long endTime = currentTime;           // 当前时间
            int step = 60;                        // 1分钟采样间隔

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            System.out.println("查询时间范围：" + sdf.format(new Date(startTime * 1000)) +
                " 至 " + sdf.format(new Date(endTime * 1000)));
            System.out.println("采样间隔：" + step + "秒");

            // 执行查询
            RealTimeBandwidthResult result = PrometheusApiUtils.getRealTimeBandwidth(
                TEST_IP, TEST_PORT, startTime, endTime, step);

            // 分析上传带宽数据
            if (result.hasUploadData()) {
                List<BandwidthData> uploadData = result.getUploadBandwidth();
                BandwidthStatistics uploadStats = result.getUploadStatistics();

                System.out.println("✅ 上传带宽数据查询成功");
                System.out.println("数据点数量：" + uploadData.size());
                System.out.println("网卡设备：" + result.getNetworkDevice());

                if (uploadStats != null) {
                    System.out.println("统计信息：");
                    System.out.println("  平均上传速率：" + uploadStats.getAvgMbpsFormatted());
                    System.out.println("  最大上传速率：" + uploadStats.getMaxMbpsFormatted());
                    System.out.println("  最小上传速率：" + uploadStats.getMinMbpsFormatted());
                    System.out.println("  使用等级：" + uploadStats.getUsageLevel());
                    System.out.println("  波动情况：" + uploadStats.getBandwidthRange());
                }

                // 显示前3个和后3个数据点
                System.out.println("数据示例（前3个）：");
                for (int i = 0; i < Math.min(3, uploadData.size()); i++) {
                    BandwidthData data = uploadData.get(i);
                    System.out.println("  " + data.getTimeString() + ": " +
                        data.getFormattedMbps() + " (" + data.getBandwidthLevel() + ")");
                }

                if (uploadData.size() > 6) {
                    System.out.println("  ... (省略中间数据) ...");
                    System.out.println("数据示例（后3个）：");
                    for (int i = uploadData.size() - 3; i < uploadData.size(); i++) {
                        BandwidthData data = uploadData.get(i);
                        System.out.println("  " + data.getTimeString() + ": " +
                            data.getFormattedMbps() + " (" + data.getBandwidthLevel() + ")");
                    }
                }

            } else {
                System.out.println("❌ 未获取到上传带宽数据");
            }

        } catch (Exception e) {
            System.err.println("❌ 上传带宽测试失败：" + e.getMessage());
        }

        System.out.println();
    }

    /**
     * 测试用例2：查询最近1小时的下载带宽
     */
    public static void testDownloadBandwidth() {
        System.out.println("【测试用例2】查询最近1小时的下载带宽");
        System.out.println("----------------------------------------");

        try {
            // 计算时间范围：最近1小时
            long currentTime = System.currentTimeMillis() / 1000;
            long startTime = currentTime - 3600; // 1小时前
            long endTime = currentTime;           // 当前时间
            int step = 60;                        // 1分钟采样间隔

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            System.out.println("查询时间范围：" + sdf.format(new Date(startTime * 1000)) +
                " 至 " + sdf.format(new Date(endTime * 1000)));
            System.out.println("采样间隔：" + step + "秒");

            // 执行查询
            RealTimeBandwidthResult result = PrometheusApiUtils.getRealTimeBandwidth(
                TEST_IP, TEST_PORT, startTime, endTime, step);

            // 分析下载带宽数据
            if (result.hasDownloadData()) {
                List<BandwidthData> downloadData = result.getDownloadBandwidth();
                BandwidthStatistics downloadStats = result.getDownloadStatistics();

                System.out.println("✅ 下载带宽数据查询成功");
                System.out.println("数据点数量：" + downloadData.size());
                System.out.println("网卡设备：" + result.getNetworkDevice());

                if (downloadStats != null) {
                    System.out.println("统计信息：");
                    System.out.println("  平均下载速率：" + downloadStats.getAvgMbpsFormatted());
                    System.out.println("  最大下载速率：" + downloadStats.getMaxMbpsFormatted());
                    System.out.println("  最小下载速率：" + downloadStats.getMinMbpsFormatted());
                    System.out.println("  使用等级：" + downloadStats.getUsageLevel());
                    System.out.println("  波动情况：" + downloadStats.getBandwidthRange());
                }

                // 显示前3个和后3个数据点
                System.out.println("数据示例（前3个）：");
                for (int i = 0; i < Math.min(3, downloadData.size()); i++) {
                    BandwidthData data = downloadData.get(i);
                    System.out.println("  " + data.getTimeString() + ": " +
                        data.getFormattedMbps() + " (" + data.getBandwidthLevel() + ")");
                }

                if (downloadData.size() > 6) {
                    System.out.println("  ... (省略中间数据) ...");
                    System.out.println("数据示例（后3个）：");
                    for (int i = downloadData.size() - 3; i < downloadData.size(); i++) {
                        BandwidthData data = downloadData.get(i);
                        System.out.println("  " + data.getTimeString() + ": " +
                            data.getFormattedMbps() + " (" + data.getBandwidthLevel() + ")");
                    }
                }

            } else {
                System.out.println("❌ 未获取到下载带宽数据");
            }

        } catch (Exception e) {
            System.err.println("❌ 下载带宽测试失败：" + e.getMessage());
        }

        System.out.println();
    }

    /**
     * 测试用例3：查询指定时间段的完整带宽数据（今天00:00-02:00）
     */
    public static void testSpecificTimeRangeBandwidth() {
        System.out.println("【测试用例3】查询今天00:00-02:00的完整带宽数据");
        System.out.println("--------------------------------------------");

        try {
            // 计算今天00:00-02:00的时间范围
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);

            long startTime = calendar.getTimeInMillis() / 1000;  // 今天00:00
            long endTime = startTime + 7200;                     // 今天02:00 (2小时)
            int step = 120;                                      // 2分钟采样间隔

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            System.out.println("查询时间范围：" + sdf.format(new Date(startTime * 1000)) +
                " 至 " + sdf.format(new Date(endTime * 1000)));
            System.out.println("采样间隔：" + step + "秒");

            // 执行查询
            RealTimeBandwidthResult result = PrometheusApiUtils.getRealTimeBandwidth(
                TEST_IP, TEST_PORT, startTime, endTime, step);

            // 输出完整的查询结果摘要
            System.out.println("✅ 完整带宽数据查询结果：");
            System.out.println(result.getSummary());

            // 对比上传和下载带宽
            if (result.hasUploadData() && result.hasDownloadData()) {
                BandwidthStatistics uploadStats = result.getUploadStatistics();
                BandwidthStatistics downloadStats = result.getDownloadStatistics();

                if (uploadStats != null && downloadStats != null) {
                    System.out.println("📊 上传 vs 下载对比：");
                    System.out.println("  上传平均速率：" + uploadStats.getAvgMbpsFormatted() +
                        " | 下载平均速率：" + downloadStats.getAvgMbpsFormatted());
                    System.out.println("  上传峰值速率：" + uploadStats.getMaxMbpsFormatted() +
                        " | 下载峰值速率：" + downloadStats.getMaxMbpsFormatted());

                    // 判断主要流量方向
                    if (uploadStats.getAvgMbps() > downloadStats.getAvgMbps()) {
                        System.out.println("  主要流量方向：上传为主");
                    } else if (downloadStats.getAvgMbps() > uploadStats.getAvgMbps()) {
                        System.out.println("  主要流量方向：下载为主");
                    } else {
                        System.out.println("  主要流量方向：上传下载均衡");
                    }
                }
            }

        } catch (Exception e) {
            System.err.println("❌ 指定时间段带宽测试失败：" + e.getMessage());
        }

        System.out.println();
    }

    /**
     * 测试用例4：查询最近3小时的带宽数据（使用便捷方法）
     */
    public static void testConvenienceMethod() {
        System.out.println("【测试用例4】使用便捷方法查询最近3小时的带宽数据");
        System.out.println("----------------------------------------------");

        try {
            // 使用便捷测试方法
            int hours = 3;
            System.out.println("调用便捷测试方法：testRealTimeBandwidth(\"" + TEST_IP + "\", \"" + TEST_PORT + "\", " + hours + ")");

            PrometheusApiUtils.testRealTimeBandwidth(TEST_IP, TEST_PORT, hours);

            System.out.println("✅ 便捷方法测试完成");

        } catch (Exception e) {
            System.err.println("❌ 便捷方法测试失败：" + e.getMessage());
        }

        System.out.println();
    }

    /**
     * 测试用例5：交互式测试（可选）
     */
    public static void interactiveTest() {
        System.out.println("【测试用例5】交互式测试（可选）");
        System.out.println("--------------------------------");
        System.out.print("是否进行交互式测试？(y/n): ");

        try {
            Scanner scanner = new Scanner(System.in);
            String input = scanner.nextLine().trim().toLowerCase();

            if ("y".equals(input) || "yes".equals(input)) {
                System.out.println("开始交互式测试...");

                // 获取用户输入的查询时长
                System.out.print("请输入查询时长（小时，1-24）: ");
                int hours = 1;
                try {
                    hours = Integer.parseInt(scanner.nextLine().trim());
                    if (hours < 1 || hours > 24) {
                        hours = 1;
                        System.out.println("输入超出范围，使用默认值：1小时");
                    }
                } catch (NumberFormatException e) {
                    System.out.println("输入格式错误，使用默认值：1小时");
                }

                // 执行自定义查询
                customBandwidthTest(hours);

            } else {
                System.out.println("跳过交互式测试");
            }

        } catch (Exception e) {
            System.err.println("❌ 交互式测试失败：" + e.getMessage());
        }

        System.out.println();
    }

    /**
     * 自定义带宽测试
     *
     * @param hours 查询时长（小时）
     */
    public static void customBandwidthTest(int hours) {
        System.out.println("执行自定义带宽测试，查询时长：" + hours + "小时");

        try {
            // 计算时间范围
            long currentTime = System.currentTimeMillis() / 1000;
            long startTime = currentTime - (hours * 3600);
            long endTime = currentTime;

            // 根据时长调整采样间隔
            int step;
            if (hours <= 1) {
                step = 60;   // 1小时内用1分钟间隔
            } else if (hours <= 6) {
                step = 300;  // 6小时内用5分钟间隔
            } else {
                step = 600;  // 超过6小时用10分钟间隔
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            System.out.println("查询时间范围：" + sdf.format(new Date(startTime * 1000)) +
                " 至 " + sdf.format(new Date(endTime * 1000)));
            System.out.println("采样间隔：" + step + "秒");

            // 执行查询
            RealTimeBandwidthResult result = PrometheusApiUtils.getRealTimeBandwidth(
                TEST_IP, TEST_PORT, startTime, endTime, step);

            // 输出详细分析
            System.out.println("📈 自定义查询结果分析：");
            System.out.println("查询时长：" + String.format("%.1f", result.getQueryDurationHours()) + "小时");
            System.out.println("总数据点：" + result.getTotalDataPoints() + "个");
            System.out.println("网卡设备：" + result.getNetworkDevice());

            // 上传带宽分析
            if (result.hasUploadData() && result.getUploadStatistics() != null) {
                BandwidthStatistics uploadStats = result.getUploadStatistics();
                System.out.println("\n📤 上传带宽分析：");
                System.out.println("  数据点数：" + uploadStats.getDataPointCount());
                System.out.println("  平均速率：" + uploadStats.getAvgMbpsFormatted());
                System.out.println("  峰值速率：" + uploadStats.getMaxMbpsFormatted());
                System.out.println("  最低速率：" + uploadStats.getMinMbpsFormatted());
                System.out.println("  使用等级：" + uploadStats.getUsageLevel());
                System.out.println("  波动情况：" + uploadStats.getBandwidthRange());
            }

            // 下载带宽分析
            if (result.hasDownloadData() && result.getDownloadStatistics() != null) {
                BandwidthStatistics downloadStats = result.getDownloadStatistics();
                System.out.println("\n📥 下载带宽分析：");
                System.out.println("  数据点数：" + downloadStats.getDataPointCount());
                System.out.println("  平均速率：" + downloadStats.getAvgMbpsFormatted());
                System.out.println("  峰值速率：" + downloadStats.getMaxMbpsFormatted());
                System.out.println("  最低速率：" + downloadStats.getMinMbpsFormatted());
                System.out.println("  使用等级：" + downloadStats.getUsageLevel());
                System.out.println("  波动情况：" + downloadStats.getBandwidthRange());
            }

            System.out.println("✅ 自定义带宽测试完成");

        } catch (Exception e) {
            System.err.println("❌ 自定义带宽测试失败：" + e.getMessage());
        }
    }

    /**
     * 快速测试方法 - 仅用于验证功能可用性
     */
    public static void quickTest() {
        System.out.println("【快速测试】验证功能可用性");
        System.out.println("------------------------");

        try {
            // 查询最近10分钟的数据
            long currentTime = System.currentTimeMillis() / 1000;
            long startTime = currentTime - 600; // 10分钟前
            long endTime = currentTime;
            int step = 60; // 1分钟间隔

            RealTimeBandwidthResult result = PrometheusApiUtils.getRealTimeBandwidth(
                TEST_IP, TEST_PORT, startTime, endTime, step);

            boolean hasData = result.hasUploadData() || result.hasDownloadData();

            if (hasData) {
                System.out.println("✅ 功能正常，成功获取到带宽数据");
                System.out.println("网卡设备：" + result.getNetworkDevice());
                System.out.println("上传数据点：" + (result.hasUploadData() ? result.getUploadBandwidth().size() : 0));
                System.out.println("下载数据点：" + (result.hasDownloadData() ? result.getDownloadBandwidth().size() : 0));
            } else {
                System.out.println("⚠️ 未获取到带宽数据，可能原因：");
                System.out.println("  1. Prometheus服务未运行或无法访问");
                System.out.println("  2. node_exporter未安装或未运行");
                System.out.println("  3. 网络连接问题");
                System.out.println("  4. 服务器IP或端口配置错误");
            }

        } catch (Exception e) {
            System.err.println("❌ 快速测试失败：" + e.getMessage());
            System.err.println("请检查：");
            System.err.println("  1. 服务器IP和端口是否正确：" + TEST_IP + ":" + TEST_PORT);
            System.err.println("  2. Prometheus服务是否正常运行");
            System.err.println("  3. 网络连接是否正常");
        }

        System.out.println();
    }
}
