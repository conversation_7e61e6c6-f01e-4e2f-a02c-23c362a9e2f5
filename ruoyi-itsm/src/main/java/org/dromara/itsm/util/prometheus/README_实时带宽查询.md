# 实时带宽查询功能使用说明

## 功能概述

新增的实时带宽查询功能可以查询服务器在指定时间段内的实际网络带宽使用情况，返回上传和下载的实际速率（单位：Mbps），不再计算使用率百分比。

## 主要特性

- ✅ **实时带宽查询**：直接返回实际传输速率，如3Mbps上传，5Mbps下载
- ✅ **自动网卡检测**：智能检测服务器的活跃物理网卡设备
- ✅ **时间范围灵活**：支持任意时间段查询，如00:00-00:59:59
- ✅ **数据统计分析**：自动计算最大值、最小值、平均值等统计信息
- ✅ **完整中文注释**：所有代码都有详细的中文注释说明
- ✅ **单位自动转换**：自动将bps转换为Mbps，便于理解

## 核心方法

### 1. getRealTimeBandwidth() - 主要查询方法

```java
public static RealTimeBandwidthResult getRealTimeBandwidth(String ip, String port, 
                                                           long start, long end, int step)
```

**参数说明：**
- `ip`: 服务器IP地址（可选，与port配合使用）
- `port`: 服务器端口号（可选，与ip配合使用）
- `start`: 查询开始时间戳（秒级Unix时间戳）
- `end`: 查询结束时间戳（秒级Unix时间戳）
- `step`: 数据采样间隔（秒），建议60-300秒

**返回值：**
- `RealTimeBandwidthResult`: 包含上传和下载带宽数据的完整结果对象

### 2. testRealTimeBandwidth() - 便捷测试方法

```java
public static void testRealTimeBandwidth(String ip, String port, int hours)
```

**参数说明：**
- `ip`: 服务器IP地址
- `port`: 服务器端口号
- `hours`: 查询时间跨度（小时）

## 使用示例

### 示例1：查询最近1小时的带宽数据

```java
// 设置服务器信息
String ip = "*************";
String port = "9100";

// 计算时间范围：最近1小时
long currentTime = System.currentTimeMillis() / 1000;
long startTime = currentTime - 3600; // 1小时前
long endTime = currentTime;           // 当前时间
int step = 60;                        // 1分钟采样间隔

// 查询实时带宽数据
RealTimeBandwidthResult result = PrometheusApiUtils.getRealTimeBandwidth(
        ip, port, startTime, endTime, step);

// 输出结果摘要
System.out.println(result.getSummary());

// 获取统计信息
if (result.getUploadStatistics() != null) {
    System.out.println("平均上传带宽：" + result.getUploadStatistics().getAvgMbpsFormatted());
}
if (result.getDownloadStatistics() != null) {
    System.out.println("平均下载带宽：" + result.getDownloadStatistics().getAvgMbpsFormatted());
}
```

### 示例2：查询指定时间段（00:00-01:00）

```java
// 计算今天00:00-01:00的时间范围
Calendar calendar = Calendar.getInstance();
calendar.set(Calendar.HOUR_OF_DAY, 0);
calendar.set(Calendar.MINUTE, 0);
calendar.set(Calendar.SECOND, 0);
calendar.set(Calendar.MILLISECOND, 0);

long startTime = calendar.getTimeInMillis() / 1000;  // 今天00:00
long endTime = startTime + 3600;                     // 今天01:00
int step = 120;                                      // 2分钟采样间隔

// 查询指定时间段的带宽数据
RealTimeBandwidthResult result = PrometheusApiUtils.getRealTimeBandwidth(
        "*************", "9100", startTime, endTime, step);
```

### 示例3：使用便捷测试方法

```java
// 查询最近3小时的带宽数据
PrometheusApiUtils.testRealTimeBandwidth("*************", "9100", 3);
```

## 数据结构说明

### RealTimeBandwidthResult - 查询结果对象

```java
public class RealTimeBandwidthResult {
    private Long startTime;                           // 查询开始时间戳
    private Long endTime;                             // 查询结束时间戳
    private Integer step;                             // 采样间隔
    private String serverInfo;                        // 服务器信息
    private String networkDevice;                     // 网卡设备名称
    private List<BandwidthData> uploadBandwidth;      // 上传带宽数据列表
    private List<BandwidthData> downloadBandwidth;    // 下载带宽数据列表
    private BandwidthStatistics uploadStatistics;    // 上传带宽统计信息
    private BandwidthStatistics downloadStatistics;  // 下载带宽统计信息
}
```

### BandwidthData - 单个数据点

```java
public class BandwidthData {
    private Long timestamp;      // 时间戳
    private String timeString;   // 格式化时间字符串
    private Float bps;           // 原始bps值
    private Float mbps;          // 转换后的Mbps值
    private String direction;    // 数据方向（"上传"或"下载"）
}
```

### BandwidthStatistics - 统计信息

```java
public class BandwidthStatistics {
    private String direction;           // 数据方向
    private Integer dataPointCount;     // 数据点总数
    private Float maxMbps;              // 最大带宽值
    private Float minMbps;              // 最小带宽值
    private Float avgMbps;              // 平均带宽值
    private String maxMbpsFormatted;    // 格式化的最大值
    private String minMbpsFormatted;    // 格式化的最小值
    private String avgMbpsFormatted;    // 格式化的平均值
}
```

## 实现逻辑详解

1. **时间范围处理**：根据传入的开始和结束时间戳构建查询时间范围
2. **网卡自动检测**：使用现有的`getActiveNetworkDevice()`方法自动检测活跃物理网卡
3. **PromQL查询**：使用`rate()`函数计算2分钟时间窗口内的字节传输速率
4. **单位转换**：将字节速率转换为比特速率（×8），再转换为Mbps（÷1,000,000）
5. **数据分离**：分别查询上传（transmit）和下载（receive）数据
6. **统计计算**：自动计算最大值、最小值、平均值等统计信息
7. **结果封装**：将所有数据封装到结构化的结果对象中

## 注意事项

1. **时间戳格式**：使用秒级Unix时间戳，不是毫秒级
2. **采样间隔**：建议根据查询时间跨度调整，1小时内用60秒，3小时内用120秒
3. **网卡检测**：如果自动检测失败，会使用默认的eth0设备
4. **数据可用性**：查询结果取决于Prometheus中是否有相应的监控数据
5. **性能考虑**：大时间跨度查询时建议增大采样间隔以减少数据量

## 错误处理

- 网络连接失败时会返回空的数据列表
- 无效的时间范围会在日志中记录警告信息
- Prometheus查询异常会被捕获并记录到日志中
- 数据转换异常不会影响其他数据点的处理

## 扩展功能

可以基于现有的数据结构进一步扩展：
- 添加带宽阈值告警功能
- 实现带宽趋势分析
- 支持多服务器批量查询
- 添加数据导出功能
