package org.dromara.itsm.util.prometheus;

import org.dromara.itsm.util.prometheus.dto.BandwidthData;
import org.dromara.itsm.util.prometheus.dto.RealTimeBandwidthResult;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 实时带宽查询使用示例
 * 
 * 演示如何使用PrometheusApiUtils.getRealTimeBandwidth()方法查询服务器的实时带宽数据
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
public class RealTimeBandwidthExample {
    
    public static void main(String[] args) {
        // 示例1：查询最近1小时的带宽数据
        example1_QueryLastHour();
        
        // 示例2：查询指定时间段的带宽数据（今天00:00-01:00）
        example2_QuerySpecificTimeRange();
        
        // 示例3：使用便捷测试方法
        example3_UseTestMethod();
    }
    
    /**
     * 示例1：查询最近1小时的带宽数据
     */
    public static void example1_QueryLastHour() {
        System.out.println("===== 示例1：查询最近1小时的带宽数据 =====");
        
        // 设置服务器信息（请根据实际情况修改）
        String ip = "*************";  // 服务器IP
        String port = "9100";         // node_exporter端口
        
        // 计算时间范围：最近1小时
        long currentTime = System.currentTimeMillis() / 1000;
        long startTime = currentTime - 3600; // 1小时前
        long endTime = currentTime;           // 当前时间
        int step = 60;                        // 1分钟采样间隔
        
        try {
            // 调用实时带宽查询方法
            RealTimeBandwidthResult result = PrometheusApiUtils.getRealTimeBandwidth(
                    ip, port, startTime, endTime, step);
            
            // 输出查询结果摘要
            System.out.println(result.getSummary());
            
            // 输出具体的带宽数据（仅显示前3个数据点）
            if (result.hasUploadData()) {
                System.out.println("上传带宽数据（前3个）：");
                List<BandwidthData> uploadData = result.getUploadBandwidth();
                for (int i = 0; i < Math.min(3, uploadData.size()); i++) {
                    BandwidthData data = uploadData.get(i);
                    System.out.println(String.format("  %s: %s", 
                            data.getTimeString(), data.getFormattedMbps()));
                }
            }
            
            if (result.hasDownloadData()) {
                System.out.println("下载带宽数据（前3个）：");
                List<BandwidthData> downloadData = result.getDownloadBandwidth();
                for (int i = 0; i < Math.min(3, downloadData.size()); i++) {
                    BandwidthData data = downloadData.get(i);
                    System.out.println(String.format("  %s: %s", 
                            data.getTimeString(), data.getFormattedMbps()));
                }
            }
            
        } catch (Exception e) {
            System.err.println("查询失败：" + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 示例2：查询指定时间段的带宽数据（今天00:00-01:00）
     */
    public static void example2_QuerySpecificTimeRange() {
        System.out.println("===== 示例2：查询今天00:00-01:00的带宽数据 =====");
        
        // 设置服务器信息
        String ip = "*************";
        String port = "9100";
        
        // 计算今天00:00-01:00的时间范围
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        
        long startTime = calendar.getTimeInMillis() / 1000;  // 今天00:00
        long endTime = startTime + 3600;                     // 今天01:00
        int step = 120;                                      // 2分钟采样间隔
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        System.out.println("查询时间范围：" + sdf.format(new Date(startTime * 1000)) + 
                          " 至 " + sdf.format(new Date(endTime * 1000)));
        
        try {
            RealTimeBandwidthResult result = PrometheusApiUtils.getRealTimeBandwidth(
                    ip, port, startTime, endTime, step);
            
            System.out.println(result.getSummary());
            
            // 如果有数据，显示平均带宽
            if (result.getUploadStatistics() != null) {
                System.out.println("该时段平均上传带宽：" + result.getUploadStatistics().getAvgMbpsFormatted());
            }
            if (result.getDownloadStatistics() != null) {
                System.out.println("该时段平均下载带宽：" + result.getDownloadStatistics().getAvgMbpsFormatted());
            }
            
        } catch (Exception e) {
            System.err.println("查询失败：" + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 示例3：使用便捷测试方法
     */
    public static void example3_UseTestMethod() {
        System.out.println("===== 示例3：使用便捷测试方法 =====");
        
        // 使用便捷测试方法查询最近3小时的带宽数据
        String ip = "*************";
        String port = "9100";
        int hours = 3;
        
        PrometheusApiUtils.testRealTimeBandwidth(ip, port, hours);
    }
    
    /**
     * 获取指定时间段的带宽数据并进行简单分析
     * 
     * @param ip 服务器IP
     * @param port 服务器端口
     * @param hours 查询小时数
     */
    public static void analyzeBandwidthUsage(String ip, String port, int hours) {
        System.out.println("===== 带宽使用分析 =====");
        
        long currentTime = System.currentTimeMillis() / 1000;
        long startTime = currentTime - (hours * 3600);
        long endTime = currentTime;
        int step = hours <= 1 ? 60 : 120; // 根据时间跨度调整采样间隔
        
        try {
            RealTimeBandwidthResult result = PrometheusApiUtils.getRealTimeBandwidth(
                    ip, port, startTime, endTime, step);
            
            System.out.println("分析时间段：" + result.getFormattedTimeRange());
            System.out.println("网卡设备：" + result.getNetworkDevice());
            
            // 分析上传带宽
            if (result.hasUploadData() && result.getUploadStatistics() != null) {
                System.out.println("\n上传带宽分析：");
                System.out.println("  数据点数：" + result.getUploadStatistics().getDataPointCount());
                System.out.println("  平均速率：" + result.getUploadStatistics().getAvgMbpsFormatted());
                System.out.println("  峰值速率：" + result.getUploadStatistics().getMaxMbpsFormatted());
                System.out.println("  最低速率：" + result.getUploadStatistics().getMinMbpsFormatted());
                System.out.println("  使用等级：" + result.getUploadStatistics().getUsageLevel());
                System.out.println("  波动情况：" + result.getUploadStatistics().getBandwidthRange());
            }
            
            // 分析下载带宽
            if (result.hasDownloadData() && result.getDownloadStatistics() != null) {
                System.out.println("\n下载带宽分析：");
                System.out.println("  数据点数：" + result.getDownloadStatistics().getDataPointCount());
                System.out.println("  平均速率：" + result.getDownloadStatistics().getAvgMbpsFormatted());
                System.out.println("  峰值速率：" + result.getDownloadStatistics().getMaxMbpsFormatted());
                System.out.println("  最低速率：" + result.getDownloadStatistics().getMinMbpsFormatted());
                System.out.println("  使用等级：" + result.getDownloadStatistics().getUsageLevel());
                System.out.println("  波动情况：" + result.getDownloadStatistics().getBandwidthRange());
            }
            
        } catch (Exception e) {
            System.err.println("带宽分析失败：" + e.getMessage());
        }
    }
}
