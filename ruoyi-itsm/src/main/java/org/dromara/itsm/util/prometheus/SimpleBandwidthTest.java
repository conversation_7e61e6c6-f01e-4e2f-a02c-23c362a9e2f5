package org.dromara.itsm.util.prometheus;

import org.dromara.itsm.util.prometheus.dto.BandwidthData;
import org.dromara.itsm.util.prometheus.dto.BandwidthStatistics;
import org.dromara.itsm.util.prometheus.dto.RealTimeBandwidthResult;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 简化版带宽测试类
 * 
 * 专门用于演示上传和下载带宽的单独测试用例
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
public class SimpleBandwidthTest {
    
    // 测试服务器配置（请根据实际情况修改）
    private static final String SERVER_IP = "*************";
    private static final String SERVER_PORT = "9100";
    
    /**
     * 主方法 - 执行所有测试用例
     */
    public static void main(String[] args) {
        System.out.println("===== 简化版带宽测试 =====");
        System.out.println("测试服务器：" + SERVER_IP + ":" + SERVER_PORT);
        System.out.println("测试时间：" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        System.out.println();
        
        // 测试用例1：单独测试上传带宽
        testUploadBandwidthOnly();
        
        // 测试用例2：单独测试下载带宽
        testDownloadBandwidthOnly();
        
        // 测试用例3：同时测试上传和下载带宽
        testBothUploadAndDownload();
        
        System.out.println("===== 所有测试完成 =====");
    }
    
    /**
     * 测试用例1：单独测试上传带宽
     * 
     * 功能：查询最近1小时的上传带宽数据，并进行详细分析
     */
    public static void testUploadBandwidthOnly() {
        System.out.println("【测试用例1】单独测试上传带宽");
        System.out.println("==============================");
        
        try {
            // 设置查询参数：最近1小时，1分钟采样间隔
            long currentTime = System.currentTimeMillis() / 1000;
            long startTime = currentTime - 3600; // 1小时前
            long endTime = currentTime;           // 当前时间
            int step = 60;                        // 1分钟采样间隔
            
            // 显示查询参数
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            System.out.println("📋 查询参数：");
            System.out.println("  服务器：" + SERVER_IP + ":" + SERVER_PORT);
            System.out.println("  时间范围：" + sdf.format(new Date(startTime * 1000)) + " 至 " + sdf.format(new Date(endTime * 1000)));
            System.out.println("  采样间隔：" + step + "秒");
            System.out.println();
            
            // 执行查询
            System.out.println("🔍 正在查询上传带宽数据...");
            RealTimeBandwidthResult result = PrometheusApiUtils.getRealTimeBandwidth(
                    SERVER_IP, SERVER_PORT, startTime, endTime, step);
            
            // 分析上传带宽数据
            if (result.hasUploadData()) {
                List<BandwidthData> uploadData = result.getUploadBandwidth();
                BandwidthStatistics uploadStats = result.getUploadStatistics();
                
                System.out.println("✅ 上传带宽数据查询成功！");
                System.out.println();
                
                // 基本信息
                System.out.println("📊 基本信息：");
                System.out.println("  网卡设备：" + result.getNetworkDevice());
                System.out.println("  数据点数量：" + uploadData.size() + "个");
                System.out.println("  查询时长：" + String.format("%.1f", result.getQueryDurationHours()) + "小时");
                System.out.println();
                
                // 统计信息
                if (uploadStats != null) {
                    System.out.println("📈 上传带宽统计：");
                    System.out.println("  平均上传速率：" + uploadStats.getAvgMbpsFormatted());
                    System.out.println("  最大上传速率：" + uploadStats.getMaxMbpsFormatted());
                    System.out.println("  最小上传速率：" + uploadStats.getMinMbpsFormatted());
                    System.out.println("  使用等级：" + uploadStats.getUsageLevel());
                    System.out.println("  波动情况：" + uploadStats.getBandwidthRange());
                    System.out.println();
                }
                
                // 显示部分数据示例
                System.out.println("📋 数据示例（最新5个数据点）：");
                int showCount = Math.min(5, uploadData.size());
                for (int i = uploadData.size() - showCount; i < uploadData.size(); i++) {
                    BandwidthData data = uploadData.get(i);
                    System.out.println("  " + data.getTimeString() + ": " + 
                                     data.getFormattedMbps() + " (" + data.getBandwidthLevel() + ")");
                }
                
            } else {
                System.out.println("❌ 未获取到上传带宽数据");
                System.out.println("可能原因：网络连接问题、Prometheus服务异常或数据不存在");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 上传带宽测试失败：" + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
        System.out.println("=".repeat(50));
        System.out.println();
    }
    
    /**
     * 测试用例2：单独测试下载带宽
     * 
     * 功能：查询最近1小时的下载带宽数据，并进行详细分析
     */
    public static void testDownloadBandwidthOnly() {
        System.out.println("【测试用例2】单独测试下载带宽");
        System.out.println("==============================");
        
        try {
            // 设置查询参数：最近1小时，1分钟采样间隔
            long currentTime = System.currentTimeMillis() / 1000;
            long startTime = currentTime - 3600; // 1小时前
            long endTime = currentTime;           // 当前时间
            int step = 60;                        // 1分钟采样间隔
            
            // 显示查询参数
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            System.out.println("📋 查询参数：");
            System.out.println("  服务器：" + SERVER_IP + ":" + SERVER_PORT);
            System.out.println("  时间范围：" + sdf.format(new Date(startTime * 1000)) + " 至 " + sdf.format(new Date(endTime * 1000)));
            System.out.println("  采样间隔：" + step + "秒");
            System.out.println();
            
            // 执行查询
            System.out.println("🔍 正在查询下载带宽数据...");
            RealTimeBandwidthResult result = PrometheusApiUtils.getRealTimeBandwidth(
                    SERVER_IP, SERVER_PORT, startTime, endTime, step);
            
            // 分析下载带宽数据
            if (result.hasDownloadData()) {
                List<BandwidthData> downloadData = result.getDownloadBandwidth();
                BandwidthStatistics downloadStats = result.getDownloadStatistics();
                
                System.out.println("✅ 下载带宽数据查询成功！");
                System.out.println();
                
                // 基本信息
                System.out.println("📊 基本信息：");
                System.out.println("  网卡设备：" + result.getNetworkDevice());
                System.out.println("  数据点数量：" + downloadData.size() + "个");
                System.out.println("  查询时长：" + String.format("%.1f", result.getQueryDurationHours()) + "小时");
                System.out.println();
                
                // 统计信息
                if (downloadStats != null) {
                    System.out.println("📉 下载带宽统计：");
                    System.out.println("  平均下载速率：" + downloadStats.getAvgMbpsFormatted());
                    System.out.println("  最大下载速率：" + downloadStats.getMaxMbpsFormatted());
                    System.out.println("  最小下载速率：" + downloadStats.getMinMbpsFormatted());
                    System.out.println("  使用等级：" + downloadStats.getUsageLevel());
                    System.out.println("  波动情况：" + downloadStats.getBandwidthRange());
                    System.out.println();
                }
                
                // 显示部分数据示例
                System.out.println("📋 数据示例（最新5个数据点）：");
                int showCount = Math.min(5, downloadData.size());
                for (int i = downloadData.size() - showCount; i < downloadData.size(); i++) {
                    BandwidthData data = downloadData.get(i);
                    System.out.println("  " + data.getTimeString() + ": " + 
                                     data.getFormattedMbps() + " (" + data.getBandwidthLevel() + ")");
                }
                
            } else {
                System.out.println("❌ 未获取到下载带宽数据");
                System.out.println("可能原因：网络连接问题、Prometheus服务异常或数据不存在");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 下载带宽测试失败：" + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
        System.out.println("=".repeat(50));
        System.out.println();
    }

    /**
     * 测试用例3：同时测试上传和下载带宽
     *
     * 功能：查询最近2小时的完整带宽数据，对比分析上传和下载情况
     */
    public static void testBothUploadAndDownload() {
        System.out.println("【测试用例3】同时测试上传和下载带宽");
        System.out.println("==================================");

        try {
            // 设置查询参数：最近2小时，2分钟采样间隔
            long currentTime = System.currentTimeMillis() / 1000;
            long startTime = currentTime - 7200; // 2小时前
            long endTime = currentTime;           // 当前时间
            int step = 120;                       // 2分钟采样间隔

            // 显示查询参数
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            System.out.println("📋 查询参数：");
            System.out.println("  服务器：" + SERVER_IP + ":" + SERVER_PORT);
            System.out.println("  时间范围：" + sdf.format(new Date(startTime * 1000)) + " 至 " + sdf.format(new Date(endTime * 1000)));
            System.out.println("  采样间隔：" + step + "秒");
            System.out.println();

            // 执行查询
            System.out.println("🔍 正在查询完整带宽数据（上传+下载）...");
            RealTimeBandwidthResult result = PrometheusApiUtils.getRealTimeBandwidth(
                    SERVER_IP, SERVER_PORT, startTime, endTime, step);

            // 显示基本信息
            System.out.println("📊 查询结果概览：");
            System.out.println("  网卡设备：" + result.getNetworkDevice());
            System.out.println("  查询时长：" + String.format("%.1f", result.getQueryDurationHours()) + "小时");
            System.out.println("  总数据点：" + result.getTotalDataPoints() + "个");
            System.out.println("  上传数据：" + (result.hasUploadData() ? "✅ 有数据 (" + result.getUploadBandwidth().size() + "个点)" : "❌ 无数据"));
            System.out.println("  下载数据：" + (result.hasDownloadData() ? "✅ 有数据 (" + result.getDownloadBandwidth().size() + "个点)" : "❌ 无数据"));
            System.out.println();

            // 对比分析上传和下载
            if (result.hasUploadData() && result.hasDownloadData()) {
                BandwidthStatistics uploadStats = result.getUploadStatistics();
                BandwidthStatistics downloadStats = result.getDownloadStatistics();

                if (uploadStats != null && downloadStats != null) {
                    System.out.println("📈📉 上传 vs 下载对比分析：");
                    System.out.println();

                    // 平均速率对比
                    System.out.println("🔄 平均速率对比：");
                    System.out.println("  上传平均：" + uploadStats.getAvgMbpsFormatted() + " | 下载平均：" + downloadStats.getAvgMbpsFormatted());

                    // 峰值速率对比
                    System.out.println("⚡ 峰值速率对比：");
                    System.out.println("  上传峰值：" + uploadStats.getMaxMbpsFormatted() + " | 下载峰值：" + downloadStats.getMaxMbpsFormatted());

                    // 使用等级对比
                    System.out.println("📊 使用等级对比：");
                    System.out.println("  上传等级：" + uploadStats.getUsageLevel() + " | 下载等级：" + downloadStats.getUsageLevel());

                    // 波动情况对比
                    System.out.println("📊 波动情况对比：");
                    System.out.println("  上传波动：" + uploadStats.getBandwidthRange() + " | 下载波动：" + downloadStats.getBandwidthRange());
                    System.out.println();

                    // 判断主要流量方向
                    System.out.println("🎯 流量方向分析：");
                    float uploadAvg = uploadStats.getAvgMbps();
                    float downloadAvg = downloadStats.getAvgMbps();

                    if (uploadAvg > downloadAvg * 1.5f) {
                        System.out.println("  主要流量方向：📤 上传为主 (上传速率明显高于下载)");
                    } else if (downloadAvg > uploadAvg * 1.5f) {
                        System.out.println("  主要流量方向：📥 下载为主 (下载速率明显高于上传)");
                    } else {
                        System.out.println("  主要流量方向：⚖️ 上传下载相对均衡");
                    }

                    // 总带宽使用情况
                    float totalAvg = uploadAvg + downloadAvg;
                    System.out.println("  总平均带宽：" + String.format("%.2f Mbps", totalAvg));

                    // 带宽使用建议
                    System.out.println();
                    System.out.println("💡 带宽使用建议：");
                    if (totalAvg < 1.0f) {
                        System.out.println("  当前带宽使用较低，网络资源充足");
                    } else if (totalAvg < 10.0f) {
                        System.out.println("  当前带宽使用适中，网络状况良好");
                    } else if (totalAvg < 100.0f) {
                        System.out.println("  当前带宽使用较高，建议关注网络性能");
                    } else {
                        System.out.println("  当前带宽使用很高，建议优化网络配置或升级带宽");
                    }
                }

            } else if (result.hasUploadData()) {
                System.out.println("⚠️ 仅获取到上传数据，下载数据缺失");
            } else if (result.hasDownloadData()) {
                System.out.println("⚠️ 仅获取到下载数据，上传数据缺失");
            } else {
                System.out.println("❌ 未获取到任何带宽数据");
                System.out.println("请检查：");
                System.out.println("  1. Prometheus服务是否正常运行");
                System.out.println("  2. node_exporter是否正常工作");
                System.out.println("  3. 网络连接是否正常");
                System.out.println("  4. 服务器配置是否正确");
            }

        } catch (Exception e) {
            System.err.println("❌ 完整带宽测试失败：" + e.getMessage());
            e.printStackTrace();
        }

        System.out.println();
        System.out.println("=".repeat(50));
        System.out.println();
    }
