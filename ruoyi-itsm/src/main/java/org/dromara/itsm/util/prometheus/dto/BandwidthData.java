package org.dromara.itsm.util.prometheus.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 带宽数据实体类
 * 
 * 用于封装单个时间点的网络带宽数据，包含时间戳、原始bps值、转换后的Mbps值等信息
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BandwidthData {
    
    /**
     * 时间戳（秒级Unix时间戳）
     */
    private Long timestamp;
    
    /**
     * 格式化的时间字符串（yyyy-MM-dd HH:mm:ss）
     */
    private String timeString;
    
    /**
     * 原始带宽值（单位：bits per second）
     */
    private Float bps;
    
    /**
     * 转换后的带宽值（单位：Mbps）
     */
    private Float mbps;
    
    /**
     * 数据方向（"上传" 或 "下载"）
     */
    private String direction;
    
    /**
     * 格式化的Mbps字符串（保留2位小数 + 单位）
     */
    public String getFormattedMbps() {
        if (mbps == null) {
            return "0.00 Mbps";
        }
        return String.format("%.2f Mbps", mbps);
    }
    
    /**
     * 格式化的bps字符串（保留0位小数 + 单位）
     */
    public String getFormattedBps() {
        if (bps == null) {
            return "0 bps";
        }
        return String.format("%.0f bps", bps);
    }
    
    /**
     * 获取带宽等级描述
     * 
     * @return 带宽等级字符串
     */
    public String getBandwidthLevel() {
        if (mbps == null || mbps <= 0) {
            return "无流量";
        } else if (mbps < 1) {
            return "低带宽";
        } else if (mbps < 10) {
            return "中等带宽";
        } else if (mbps < 100) {
            return "高带宽";
        } else {
            return "超高带宽";
        }
    }
}
