package org.dromara.itsm.util.prometheus.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 带宽统计信息实体类
 * 
 * 用于封装某个方向（上传或下载）的带宽统计数据，包含最大值、最小值、平均值等统计信息
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BandwidthStatistics {
    
    /**
     * 数据方向（"上传" 或 "下载"）
     */
    private String direction;
    
    /**
     * 数据点总数
     */
    private Integer dataPointCount;
    
    /**
     * 最大带宽值（Mbps）
     */
    private Float maxMbps;
    
    /**
     * 最小带宽值（Mbps）
     */
    private Float minMbps;
    
    /**
     * 平均带宽值（Mbps）
     */
    private Float avgMbps;
    
    /**
     * 格式化的最大带宽值字符串
     */
    private String maxMbpsFormatted;
    
    /**
     * 格式化的最小带宽值字符串
     */
    private String minMbpsFormatted;
    
    /**
     * 格式化的平均带宽值字符串
     */
    private String avgMbpsFormatted;
    
    /**
     * 获取带宽变化范围描述
     * 
     * @return 带宽变化范围字符串
     */
    public String getBandwidthRange() {
        if (maxMbps == null || minMbps == null) {
            return "无数据";
        }
        
        float range = maxMbps - minMbps;
        if (range < 0.1f) {
            return "带宽稳定";
        } else if (range < 1.0f) {
            return "带宽轻微波动";
        } else if (range < 10.0f) {
            return "带宽中等波动";
        } else {
            return "带宽大幅波动";
        }
    }
    
    /**
     * 获取带宽使用等级
     * 
     * @return 带宽使用等级字符串
     */
    public String getUsageLevel() {
        if (avgMbps == null || avgMbps <= 0) {
            return "无使用";
        } else if (avgMbps < 1) {
            return "轻度使用";
        } else if (avgMbps < 10) {
            return "中度使用";
        } else if (avgMbps < 100) {
            return "重度使用";
        } else {
            return "超重度使用";
        }
    }
    
    /**
     * 获取统计摘要信息
     * 
     * @return 统计摘要字符串
     */
    public String getSummary() {
        if (dataPointCount == null || dataPointCount == 0) {
            return String.format("%s带宽：无数据", direction);
        }
        
        return String.format("%s带宽统计：共%d个数据点，平均%s，最大%s，最小%s，%s", 
                direction, dataPointCount, avgMbpsFormatted, maxMbpsFormatted, 
                minMbpsFormatted, getBandwidthRange());
    }
}
