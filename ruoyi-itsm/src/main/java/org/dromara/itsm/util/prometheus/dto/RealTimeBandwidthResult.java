package org.dromara.itsm.util.prometheus.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 实时带宽查询结果实体类
 * 
 * 用于封装服务器实时带宽查询的完整结果，包含上传和下载的带宽数据、统计信息以及查询参数等
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RealTimeBandwidthResult {
    
    /**
     * 查询开始时间戳（秒级Unix时间戳）
     */
    private Long startTime;
    
    /**
     * 查询结束时间戳（秒级Unix时间戳）
     */
    private Long endTime;
    
    /**
     * 数据采样间隔（秒）
     */
    private Integer step;
    
    /**
     * 服务器信息（IP:端口）
     */
    private String serverInfo;
    
    /**
     * 检测到的网卡设备名称
     */
    private String networkDevice;
    
    /**
     * 上传带宽数据列表
     */
    private List<BandwidthData> uploadBandwidth;
    
    /**
     * 下载带宽数据列表
     */
    private List<BandwidthData> downloadBandwidth;
    
    /**
     * 上传带宽统计信息
     */
    private BandwidthStatistics uploadStatistics;
    
    /**
     * 下载带宽统计信息
     */
    private BandwidthStatistics downloadStatistics;
    
    /**
     * 获取格式化的查询时间范围
     * 
     * @return 格式化的时间范围字符串
     */
    public String getFormattedTimeRange() {
        if (startTime == null || endTime == null) {
            return "时间范围未知";
        }
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTimeStr = sdf.format(new Date(startTime * 1000));
        String endTimeStr = sdf.format(new Date(endTime * 1000));
        
        return String.format("%s 至 %s", startTimeStr, endTimeStr);
    }
    
    /**
     * 获取查询时长（分钟）
     * 
     * @return 查询时长（分钟）
     */
    public Long getQueryDurationMinutes() {
        if (startTime == null || endTime == null) {
            return 0L;
        }
        return (endTime - startTime) / 60;
    }
    
    /**
     * 获取查询时长（小时）
     * 
     * @return 查询时长（小时）
     */
    public Double getQueryDurationHours() {
        if (startTime == null || endTime == null) {
            return 0.0;
        }
        return (endTime - startTime) / 3600.0;
    }
    
    /**
     * 检查是否有上传数据
     * 
     * @return 是否有上传数据
     */
    public boolean hasUploadData() {
        return uploadBandwidth != null && !uploadBandwidth.isEmpty();
    }
    
    /**
     * 检查是否有下载数据
     * 
     * @return 是否有下载数据
     */
    public boolean hasDownloadData() {
        return downloadBandwidth != null && !downloadBandwidth.isEmpty();
    }
    
    /**
     * 获取总数据点数量
     * 
     * @return 总数据点数量
     */
    public int getTotalDataPoints() {
        int uploadCount = hasUploadData() ? uploadBandwidth.size() : 0;
        int downloadCount = hasDownloadData() ? downloadBandwidth.size() : 0;
        return uploadCount + downloadCount;
    }
    
    /**
     * 获取查询结果摘要
     * 
     * @return 查询结果摘要字符串
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("实时带宽查询结果摘要：\n");
        summary.append("查询时间：").append(getFormattedTimeRange()).append("\n");
        summary.append("查询时长：").append(String.format("%.1f", getQueryDurationHours())).append("小时\n");
        summary.append("服务器：").append(serverInfo).append("\n");
        summary.append("网卡设备：").append(networkDevice).append("\n");
        summary.append("采样间隔：").append(step).append("秒\n");
        
        if (hasUploadData()) {
            summary.append("上传数据：").append(uploadBandwidth.size()).append("个数据点\n");
            if (uploadStatistics != null) {
                summary.append("  ").append(uploadStatistics.getSummary()).append("\n");
            }
        } else {
            summary.append("上传数据：无数据\n");
        }
        
        if (hasDownloadData()) {
            summary.append("下载数据：").append(downloadBandwidth.size()).append("个数据点\n");
            if (downloadStatistics != null) {
                summary.append("  ").append(downloadStatistics.getSummary()).append("\n");
            }
        } else {
            summary.append("下载数据：无数据\n");
        }
        
        return summary.toString();
    }
}
