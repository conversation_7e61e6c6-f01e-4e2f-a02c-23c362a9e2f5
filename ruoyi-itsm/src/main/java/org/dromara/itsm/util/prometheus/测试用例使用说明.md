# 实时带宽查询测试用例使用说明

## 概述

我已经为您创建了完整的实时带宽查询功能和相应的测试用例。现在您可以通过运行测试类来验证功能的正确性。

## 测试类说明

### 1. SimpleBandwidthTest.java - 简化版测试类（推荐）

这是专门为演示上传和下载带宽测试而创建的简化版测试类。

**特点：**
- 代码简洁，易于理解
- 专注于上传和下载带宽的单独测试
- 包含详细的输出信息和分析

**测试用例：**
- `testUploadBandwidthOnly()` - 单独测试上传带宽
- `testDownloadBandwidthOnly()` - 单独测试下载带宽  
- `testBothUploadAndDownload()` - 同时测试上传和下载带宽

### 2. BandwidthTestMain.java - 完整版测试类

这是功能完整的测试类，包含更多高级功能。

**特点：**
- 功能全面，包含交互式测试
- 支持自定义时间范围
- 包含便捷测试方法

## 快速开始

### 步骤1：配置服务器信息

在运行测试之前，请确保修改测试类中的服务器配置：

```java
// 在 SimpleBandwidthTest.java 中修改这些常量
private static final String SERVER_IP = "*************";    // 改为您的服务器IP
private static final String SERVER_PORT = "9100";           // 改为您的node_exporter端口
```

### 步骤2：运行简化版测试

直接运行 `SimpleBandwidthTest.java` 的main方法：

```bash
# 在IDE中右键点击 SimpleBandwidthTest.java -> Run 'SimpleBandwidthTest.main()'
# 或者使用命令行：
java org.dromara.itsm.util.prometheus.SimpleBandwidthTest
```

### 步骤3：查看测试结果

测试将按顺序执行以下三个用例：

1. **测试用例1：单独测试上传带宽**
   - 查询最近1小时的上传带宽数据
   - 显示平均、最大、最小上传速率
   - 展示最新5个数据点

2. **测试用例2：单独测试下载带宽**
   - 查询最近1小时的下载带宽数据
   - 显示平均、最大、最小下载速率
   - 展示最新5个数据点

3. **测试用例3：同时测试上传和下载带宽**
   - 查询最近2小时的完整带宽数据
   - 对比分析上传和下载情况
   - 提供带宽使用建议

## 预期输出示例

```
===== 简化版带宽测试 =====
测试服务器：*************:9100
测试时间：2024-12-19 14:30:00

【测试用例1】单独测试上传带宽
==============================
📋 查询参数：
  服务器：*************:9100
  时间范围：2024-12-19 13:30:00 至 2024-12-19 14:30:00
  采样间隔：60秒

🔍 正在查询上传带宽数据...
✅ 上传带宽数据查询成功！

📊 基本信息：
  网卡设备：ens160
  数据点数量：60个
  查询时长：1.0小时

📈 上传带宽统计：
  平均上传速率：3.25 Mbps
  最大上传速率：8.50 Mbps
  最小上传速率：0.15 Mbps
  使用等级：中度使用
  波动情况：带宽中等波动

📋 数据示例（最新5个数据点）：
  2024-12-19 14:26:00: 3.20 Mbps (中等带宽)
  2024-12-19 14:27:00: 3.45 Mbps (中等带宽)
  2024-12-19 14:28:00: 2.98 Mbps (中等带宽)
  2024-12-19 14:29:00: 3.67 Mbps (中等带宽)
  2024-12-19 14:30:00: 3.12 Mbps (中等带宽)
```

## 自定义测试

### 修改查询时间范围

您可以在测试方法中修改时间参数：

```java
// 修改查询时长（以秒为单位）
long startTime = currentTime - 3600;  // 3600秒 = 1小时
long startTime = currentTime - 7200;  // 7200秒 = 2小时
long startTime = currentTime - 10800; // 10800秒 = 3小时
```

### 修改采样间隔

```java
// 修改采样间隔
int step = 60;   // 1分钟间隔
int step = 120;  // 2分钟间隔
int step = 300;  // 5分钟间隔
```

### 查询特定时间段

```java
// 查询今天00:00-01:00的数据
Calendar calendar = Calendar.getInstance();
calendar.set(Calendar.HOUR_OF_DAY, 0);
calendar.set(Calendar.MINUTE, 0);
calendar.set(Calendar.SECOND, 0);
calendar.set(Calendar.MILLISECOND, 0);

long startTime = calendar.getTimeInMillis() / 1000;  // 今天00:00
long endTime = startTime + 3600;                     // 今天01:00
```

## 故障排除

### 1. 无法获取数据

如果测试显示"❌ 未获取到带宽数据"，请检查：

- **Prometheus服务状态**：确保Prometheus服务正在运行
- **node_exporter状态**：确保目标服务器上的node_exporter正在运行
- **网络连接**：确保测试机器能够访问Prometheus服务
- **服务器配置**：确认IP地址和端口号是否正确

### 2. 连接超时

如果出现连接超时错误：

- 检查防火墙设置
- 确认Prometheus API端点是否可访问
- 验证网络连通性

### 3. 数据不完整

如果只能获取到上传或下载数据：

- 检查网卡设备是否正确检测
- 确认监控指标是否完整
- 查看Prometheus日志是否有错误

## 高级用法

### 使用完整版测试类

如果需要更多功能，可以运行 `BandwidthTestMain.java`：

```java
// 运行完整测试套件
BandwidthTestMain.main(new String[]{});

// 或者运行特定测试
BandwidthTestMain.testUploadBandwidth();
BandwidthTestMain.testDownloadBandwidth();
```

### 使用便捷测试方法

```java
// 直接使用PrometheusApiUtils的便捷方法
PrometheusApiUtils.testRealTimeBandwidth("*************", "9100", 3);
```

### 编程式调用

```java
// 在您的代码中直接调用
RealTimeBandwidthResult result = PrometheusApiUtils.getRealTimeBandwidth(
    "*************", "9100", startTime, endTime, step);

// 获取上传带宽统计
if (result.hasUploadData()) {
    BandwidthStatistics uploadStats = result.getUploadStatistics();
    System.out.println("平均上传速率：" + uploadStats.getAvgMbpsFormatted());
}

// 获取下载带宽统计
if (result.hasDownloadData()) {
    BandwidthStatistics downloadStats = result.getDownloadStatistics();
    System.out.println("平均下载速率：" + downloadStats.getAvgMbpsFormatted());
}
```

## 注意事项

1. **时间戳格式**：所有时间戳都是秒级Unix时间戳，不是毫秒级
2. **数据可用性**：查询结果取决于Prometheus中是否有相应的监控数据
3. **网卡检测**：系统会自动检测活跃的物理网卡，如果检测失败会使用默认值
4. **性能考虑**：大时间跨度查询时建议增大采样间隔以减少数据量

## 总结

通过这些测试用例，您可以：
- ✅ 验证实时带宽查询功能是否正常工作
- ✅ 查看实际的上传和下载速率（Mbps）
- ✅ 分析带宽使用模式和趋势
- ✅ 获取详细的统计信息和使用建议

建议先运行 `SimpleBandwidthTest.java` 进行基本功能验证，然后根据需要使用其他高级功能。
